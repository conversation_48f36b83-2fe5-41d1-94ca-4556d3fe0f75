import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';

enum AssignmentType {
  /// Homework assigned to an entire class
  classAssignment,

  /// Homework assigned to specific individual students
  individual,

  /// Homework assigned to a group of students
  group,

  /// Custom assignment type for future extensibility
  custom,
}

/// Extension to provide additional functionality for AssignmentType
extension AssignmentTypeExtension on AssignmentType {
  /// Returns a human-readable label for the assignment type
  String get label {
    switch (this) {
      case AssignmentType.classAssignment:
        return 'Class Assignment';
      case AssignmentType.individual:
        return 'Individual Assignment';
      case AssignmentType.group:
        return 'Group Assignment';
      case AssignmentType.custom:
        return 'Custom Assignment';
    }
  }

  /// Returns a short label for the assignment type (for UI space constraints)
  String get shortLabel {
    switch (this) {
      case AssignmentType.classAssignment:
        return 'Class';
      case AssignmentType.individual:
        return 'Individual';
      case AssignmentType.group:
        return 'Group';
      case AssignmentType.custom:
        return 'Custom';
    }
  }

  /// Returns the color associated with the assignment type
  Color get color {
    switch (this) {
      case AssignmentType.classAssignment:
        return Colors.blue;
      case AssignmentType.individual:
        return Colors.green;
      case AssignmentType.group:
        return Colors.orange;
      case AssignmentType.custom:
        return Colors.purple;
    }
  }

  /// Returns an icon associated with the assignment type
  IconData get icon {
    switch (this) {
      case AssignmentType.classAssignment:
        return Symbols.school;
      case AssignmentType.individual:
        return Symbols.person;
      case AssignmentType.group:
        return Symbols.group;
      case AssignmentType.custom:
        return Symbols.settings;
    }
  }

  /// Returns whether this assignment type requires a class ID
  bool get requiresClassId {
    switch (this) {
      case AssignmentType.classAssignment:
        return true;
      case AssignmentType.individual:
      case AssignmentType.group:
      case AssignmentType.custom:
        return false;
    }
  }

  /// Returns whether this assignment type requires assigned user IDs
  bool get requiresAssignedUserIds {
    switch (this) {
      case AssignmentType.individual:
      case AssignmentType.group:
      case AssignmentType.custom:
        return true;
      case AssignmentType.classAssignment:
        return false;
    }
  }

  /// Returns a description of the assignment type
  String get description {
    switch (this) {
      case AssignmentType.classAssignment:
        return 'Assigned to all students in a class';
      case AssignmentType.individual:
        return 'Assigned to specific individual students';
      case AssignmentType.group:
        return 'Assigned to a group of students';
      case AssignmentType.custom:
        return 'Custom assignment configuration';
    }
  }
}
