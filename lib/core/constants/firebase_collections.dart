/// Firebase Firestore collection names used throughout the app
///
/// This file centralizes all collection names to ensure consistency
/// and make it easier to update collection names if needed.
class FirebaseCollections {
  // Private constructor to prevent instantiation
  FirebaseCollections._();

  // User and Profile collections
  static const String users = 'users';
  static const String profiles = 'profiles';

  // Classroom collections
  static const String classrooms = 'classrooms';
  static const String classroomActivities = 'classroom_activities';

  // Homework collections
  static const String homeworks = 'homeworks';
  static const String homeworkSubmissions = 'homework_submissions';

  // Authentication collections
  static const String authSessions = 'auth_sessions';
  static const String userTokens = 'user_tokens';

  // Notification collections
  static const String notifications = 'notifications';
  static const String deviceTokens = 'device_tokens';

  // Analytics collections
  static const String userAnalytics = 'user_analytics';
  static const String appEvents = 'app_events';

  // File storage collections
  static const String fileMetadata = 'file_metadata';
  static const String uploadSessions = 'upload_sessions';

  // Digital Library collections
  static const String libraryFiles = 'library_files';
  static const String libraryFolders = 'library_folders';
  static const String libraryUploadSessions = 'library_upload_sessions';
  static const String libraryFileShares = 'library_file_shares';

  // Announcements collections
  static const String announcements = 'announcements';
  static const String announcementReads = 'announcement_reads';

  // Study Plan collections
  static const String studyPlans = 'study_plans';
  static const String studyPlanSections = 'study_plan_sections';
  static const String studyPlanTasks = 'study_plan_tasks';
  static const String taskResources = 'task_resources';
  static const String taskSubmissions = 'task_submissions';

  // Chat collections
  static const String chats = 'chats';
  static const String chatMessages = 'chat_messages';
  static const String chatParticipants = 'chat_participants';
  static const String messageReactions = 'message_reactions';
  static const String chatAttachments = 'chat_attachments';
  static const String userPresence = 'user_presence';
  static const String typingIndicators = 'typing_indicators';
  static const String messageStatus = 'message_status';
  static const String chatSettings = 'chat_settings';
  static const String blockedUsers = 'blocked_users';

  // Settings collections
  static const String appSettings = 'app_settings';
  static const String userPreferences = 'user_preferences';

  // Debug collections (for development/testing)
  static const String debugLogs = 'debug_logs';
  static const String testData = 'test_data';
}
