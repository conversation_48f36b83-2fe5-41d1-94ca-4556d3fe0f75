import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../../core/routes/app_routes.dart';
import '../../controllers/announcement_search_controller.dart';
import '../../controllers/announcement_filter_controller.dart';

import 'sections/search_bar_section.dart';
import 'sections/search_filter_tabs_section.dart';
import 'sections/search_results_section.dart';
import 'sections/search_suggestions_section.dart';
import 'sections/recent_searches_section.dart';

/// Advanced search functionality for announcements
class SearchScreen extends ConsumerStatefulWidget {
  final String? initialQuery;

  const SearchScreen({super.key, this.initialQuery});

  @override
  ConsumerState<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends ConsumerState<SearchScreen> {
  late TextEditingController _searchController;
  late FocusNode _searchFocusNode;

  bool _isSearching = false;
  bool _hasSearched = false;
  final String _sortBy = 'date';

  final List<String> _filterTabs = [
    'All',
    'Notices',
    'Reminders',
    'News',
    'Alerts',
  ];

  final List<String> _sortOptions = ['Date', 'Priority', 'Type', 'Relevance'];

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController(text: widget.initialQuery ?? '');
    _searchFocusNode = FocusNode();

    if (widget.initialQuery != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final searchNotifier = ref.read(
          announcementSearchQueryProvider.notifier,
        );
        searchNotifier.setQuery(widget.initialQuery!);
        _performSearch();
      });
    }

    // Auto-focus search bar
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _searchFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: _buildAppBar(theme),
      body: SafeArea(
        child: Column(
          children: [
            // Search bar section
            SearchBarSection(
              controller: _searchController,
              focusNode: _searchFocusNode,
              onSearchChanged: _onSearchChanged,
              onSearchSubmitted: _onSearchSubmitted,
              onClearPressed: _onClearPressed,
            ),

            // Filter tabs section
            SearchFilterTabsSection(
              tabs: _filterTabs,
              selectedIndex: 0, // Default to 'All' for search
              onTabChanged: _onFilterChanged,
            ),

            // Main content
            Expanded(child: _buildMainContent()),
          ],
        ),
      ),
    );
  }

  AppBar _buildAppBar(ThemeData theme) {
    return AppBar(
      backgroundColor: theme.colorScheme.surface,
      elevation: 0,
      leading: IconButton(
        onPressed: () => Navigator.of(context).pop(),
        icon: Icon(Symbols.arrow_back_ios, size: 24.sp),
      ),
      title: Text(
        'Search Announcements',
        style: theme.textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      actions: [
        // Sort button
        PopupMenuButton<String>(
          onSelected: _onSortChanged,
          icon: Icon(Symbols.sort, size: 24.sp),
          tooltip: 'Sort results',
          itemBuilder: (context) => _sortOptions
              .map(
                (option) => PopupMenuItem(
                  value: option.toLowerCase(),
                  child: Row(
                    children: [
                      Icon(_getSortIcon(option.toLowerCase()), size: 18.sp),
                      SizedBox(width: 8.w),
                      Text(option),
                      if (_sortBy == option.toLowerCase()) ...[
                        const Spacer(),
                        Icon(
                          Symbols.check,
                          size: 16.sp,
                          color: theme.colorScheme.primary,
                        ),
                      ],
                    ],
                  ),
                ),
              )
              .toList(),
        ),

        SizedBox(width: 8.w),
      ],
    );
  }

  Widget _buildMainContent() {
    final searchQuery = ref.watch(announcementSearchQueryProvider);

    if (_isSearching) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_hasSearched) {
      return SearchResultsSection(
        query: searchQuery,
        filter: 'All', // Default filter for search
        sortBy: 'date', // Default sort
        onResultTap: _onResultTap,
      );
    }

    // Show initial state with suggestions and recent searches
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search suggestions
          if (searchQuery.isNotEmpty)
            SearchSuggestionsSection(
              query: searchQuery,
              onSuggestionTap: _onSuggestionTap,
            )
          else ...[
            // Recent searches
            RecentSearchesSection(
              onRecentSearchTap: _onRecentSearchTap,
              onClearRecentSearches: _onClearRecentSearches,
            ),
          ],
        ],
      ),
    );
  }

  void _onSearchChanged(String query) {
    final searchNotifier = ref.read(announcementSearchQueryProvider.notifier);
    searchNotifier.setQuery(query);

    setState(() {
      _hasSearched = false;
    });
  }

  void _onSearchSubmitted(String query) {
    if (query.trim().isNotEmpty) {
      _performSearch();
    }
  }

  void _onClearPressed() {
    _searchController.clear();
    final searchNotifier = ref.read(announcementSearchQueryProvider.notifier);
    searchNotifier.clearQuery();

    setState(() {
      _hasSearched = false;
    });
  }

  void _onFilterChanged(int index) {
    // For search screen, we don't need to update global filter state
    // Just trigger search if already searched
    if (_hasSearched) {
      _performSearch();
    }
  }

  void _onSortChanged(String sortBy) {
    // For search screen, sorting is handled locally
    if (_hasSearched) {
      _performSearch();
    }
  }

  void _performSearch() {
    final searchQuery = ref.read(announcementSearchQueryProvider);
    if (searchQuery.trim().isEmpty) return;

    setState(() {
      _isSearching = true;
    });

    // Trigger search with current query
    // The search results will be handled by the SearchResultsSection
    Future.delayed(const Duration(milliseconds: 800), () {
      if (mounted) {
        setState(() {
          _isSearching = false;
          _hasSearched = true;
        });
      }
    });

    // Save to recent searches
    final recentSearchesNotifier = ref.read(recentSearchesProvider.notifier);
    recentSearchesNotifier.addSearch(searchQuery);
  }

  void _onSuggestionTap(String suggestion) {
    _searchController.text = suggestion;
    final searchNotifier = ref.read(announcementSearchQueryProvider.notifier);
    searchNotifier.setQuery(suggestion);
    _performSearch();
  }

  void _onRecentSearchTap(String query) {
    _searchController.text = query;
    final searchNotifier = ref.read(announcementSearchQueryProvider.notifier);
    searchNotifier.setQuery(query);
    _performSearch();
  }

  void _onResultTap(String announcementId) {
    // Navigate to announcement detail
    context.pushNamed(
      RouteNames.announcementDetail,
      pathParameters: {'id': announcementId},
    );
  }

  void _onClearRecentSearches() {
    final recentSearchesNotifier = ref.read(recentSearchesProvider.notifier);
    recentSearchesNotifier.clearAll();
  }

  IconData _getSortIcon(String sortBy) {
    switch (sortBy) {
      case 'date':
        return Symbols.schedule;
      case 'priority':
        return Symbols.priority_high;
      case 'type':
        return Symbols.category;
      case 'relevance':
        return Symbols.trending_up;
      default:
        return Symbols.sort;
    }
  }
}
