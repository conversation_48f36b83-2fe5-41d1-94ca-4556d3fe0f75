import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';
import '../../../core/enums/homework/assignment_type.dart';
import '../controllers/unified_homework_controller.dart';

/// Configuration class for homework filters
/// This design allows easy extension for future assignment types
class HomeworkFilterConfig {
  final UnifiedHomeworkFilterType type;
  final String label;
  final String shortLabel;
  final String description;
  final IconData icon;
  final Color color;
  final AssignmentType? assignmentType;
  final bool isDefault;

  const HomeworkFilterConfig({
    required this.type,
    required this.label,
    required this.shortLabel,
    required this.description,
    required this.icon,
    required this.color,
    this.assignmentType,
    this.isDefault = false,
  });

  /// Predefined filter configurations
  static const List<HomeworkFilterConfig> defaultConfigs = [
    HomeworkFilterConfig(
      type: UnifiedHomeworkFilterType.all,
      label: 'All Homework',
      shortLabel: 'All',
      description: 'View all your homework assignments in one place',
      icon: Symbols.assignment,
      color: Colors.blue,
      isDefault: true,
    ),
    HomeworkFilterConfig(
      type: UnifiedHomeworkFilterType.classAssignments,
      label: 'Class Assignments',
      shortLabel: 'Class',
      description: 'Assignments given to your entire class',
      icon: Symbols.school,
      color: Colors.green,
      assignmentType: AssignmentType.classAssignment,
    ),
    HomeworkFilterConfig(
      type: UnifiedHomeworkFilterType.individual,
      label: 'Individual Tasks',
      shortLabel: 'Individual',
      description: 'Tasks assigned specifically to you',
      icon: Symbols.person,
      color: Colors.orange,
      assignmentType: AssignmentType.individual,
    ),
    HomeworkFilterConfig(
      type: UnifiedHomeworkFilterType.group,
      label: 'Group Projects',
      shortLabel: 'Group',
      description: 'Collaborative projects with your classmates',
      icon: Symbols.group,
      color: Colors.purple,
      assignmentType: AssignmentType.group,
    ),
  ];

  /// Get configuration for a specific filter type
  static HomeworkFilterConfig? getConfig(UnifiedHomeworkFilterType type) {
    try {
      return defaultConfigs.firstWhere((config) => config.type == type);
    } catch (e) {
      return null;
    }
  }

  /// Get all available filter configurations
  static List<HomeworkFilterConfig> getAllConfigs() {
    return List.from(defaultConfigs);
  }

  /// Get the default filter configuration
  static HomeworkFilterConfig getDefaultConfig() {
    return defaultConfigs.firstWhere(
      (config) => config.isDefault,
      orElse: () => defaultConfigs.first,
    );
  }

  /// Check if a filter type requires class selection
  static bool requiresClassSelection(UnifiedHomeworkFilterType type) {
    return type == UnifiedHomeworkFilterType.classAssignments;
  }

  /// Get user-friendly error messages for filter operations
  static String getErrorMessage(UnifiedHomeworkFilterType type, String error) {
    final config = getConfig(type);
    if (config == null) {
      return 'Failed to load homework: $error';
    }

    switch (type) {
      case UnifiedHomeworkFilterType.all:
        return 'Unable to load your homework. Please check your connection and try again.';
      case UnifiedHomeworkFilterType.classAssignments:
        return 'Unable to load class assignments. Please check your connection and try again.';
      case UnifiedHomeworkFilterType.individual:
        return 'Unable to load your individual tasks. Please check your connection and try again.';
      case UnifiedHomeworkFilterType.group:
        return 'Unable to load group projects. Please check your connection and try again.';
    }
  }

  /// Get empty state messages for different filter types
  static String getEmptyStateMessage(UnifiedHomeworkFilterType type) {
    switch (type) {
      case UnifiedHomeworkFilterType.all:
        return 'No homework assignments found. Check back later for new assignments.';
      case UnifiedHomeworkFilterType.classAssignments:
        return 'No class assignments available. Your teacher hasn\'t assigned any class work yet.';
      case UnifiedHomeworkFilterType.individual:
        return 'No individual tasks assigned. You\'re all caught up!';
      case UnifiedHomeworkFilterType.group:
        return 'No group projects available. Check back for collaborative assignments.';
    }
  }

  /// Get loading messages for different filter types
  static String getLoadingMessage(UnifiedHomeworkFilterType type) {
    switch (type) {
      case UnifiedHomeworkFilterType.all:
        return 'Loading all your homework...';
      case UnifiedHomeworkFilterType.classAssignments:
        return 'Loading class assignments...';
      case UnifiedHomeworkFilterType.individual:
        return 'Loading your individual tasks...';
      case UnifiedHomeworkFilterType.group:
        return 'Loading group projects...';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is HomeworkFilterConfig && other.type == type;
  }

  @override
  int get hashCode => type.hashCode;

  @override
  String toString() {
    return 'HomeworkFilterConfig(type: $type, label: $label)';
  }
}

/// Extension to add filter configuration methods to UnifiedHomeworkFilterType
extension UnifiedHomeworkFilterTypeConfigExtension
    on UnifiedHomeworkFilterType {
  /// Get the configuration for this filter type
  HomeworkFilterConfig? get config => HomeworkFilterConfig.getConfig(this);

  /// Get the user-friendly label
  String get displayLabel => config?.label ?? toString();

  /// Get the short label for UI
  String get displayShortLabel => config?.shortLabel ?? toString();

  /// Get the description
  String get displayDescription => config?.description ?? '';

  /// Get the icon
  IconData get displayIcon => config?.icon ?? Symbols.assignment;

  /// Get the color
  Color get displayColor => config?.color ?? Colors.blue;

  /// Check if this filter requires class selection
  bool get requiresClassSelection =>
      HomeworkFilterConfig.requiresClassSelection(this);

  /// Get error message for this filter type
  String getErrorMessage(String error) =>
      HomeworkFilterConfig.getErrorMessage(this, error);

  /// Get empty state message for this filter type
  String get emptyStateMessage =>
      HomeworkFilterConfig.getEmptyStateMessage(this);

  /// Get loading message for this filter type
  String get loadingMessage => HomeworkFilterConfig.getLoadingMessage(this);
}

/// Helper class for managing filter state and transitions
class HomeworkFilterManager {
  /// Validate if a filter transition is allowed
  static bool canTransitionTo(
    UnifiedHomeworkFilterType from,
    UnifiedHomeworkFilterType to,
  ) {
    // All transitions are allowed in the current implementation
    // This method can be extended for complex business rules
    return true;
  }

  /// Get recommended next filters based on current selection
  static List<UnifiedHomeworkFilterType> getRecommendedFilters(
    UnifiedHomeworkFilterType current,
    Map<UnifiedHomeworkFilterType, int> counts,
  ) {
    final recommendations = <UnifiedHomeworkFilterType>[];

    // If currently viewing all, recommend filters with content
    if (current == UnifiedHomeworkFilterType.all) {
      for (final entry in counts.entries) {
        if (entry.key != UnifiedHomeworkFilterType.all && entry.value > 0) {
          recommendations.add(entry.key);
        }
      }
    } else {
      // If viewing a specific type, recommend "All" and other types with content
      recommendations.add(UnifiedHomeworkFilterType.all);
      for (final entry in counts.entries) {
        if (entry.key != current &&
            entry.key != UnifiedHomeworkFilterType.all &&
            entry.value > 0) {
          recommendations.add(entry.key);
        }
      }
    }

    return recommendations;
  }

  /// Get filter analytics data
  static Map<String, dynamic> getFilterAnalytics(
    UnifiedHomeworkFilterType filterType,
    int itemCount,
    Duration loadTime,
  ) {
    return {
      'filter_type': filterType.toString(),
      'item_count': itemCount,
      'load_time_ms': loadTime.inMilliseconds,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}
