import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../models/flexible_schedule.dart';

/// A reusable widget for selecting schedule types with tab-like capsule design
class ScheduleSelectionWidget extends StatefulWidget {
  final SchedulingType selectedType;
  final ValueChanged<SchedulingType> onTypeChanged;
  final DateTime? selectedDate;
  final DateTime? startDate;
  final DateTime? endDate;
  final Duration? duration;
  final ValueChanged<DateTime?>? onSelectedDateChanged;
  final ValueChanged<DateTime?>? onStartDateChanged;
  final ValueChanged<DateTime?>? onEndDateChanged;
  final ValueChanged<Duration?>? onDurationChanged;
  final bool showValidation;
  final String? validationMessage;

  const ScheduleSelectionWidget({
    super.key,
    required this.selectedType,
    required this.onTypeChanged,
    this.selectedDate,
    this.startDate,
    this.endDate,
    this.duration,
    this.onSelectedDateChanged,
    this.onStartDateChanged,
    this.onEndDateChanged,
    this.onDurationChanged,
    this.showValidation = false,
    this.validationMessage,
  });

  @override
  State<ScheduleSelectionWidget> createState() =>
      _ScheduleSelectionWidgetState();
}

class _ScheduleSelectionWidgetState extends State<ScheduleSelectionWidget> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Tab-like capsule selection
        _buildScheduleTypeSelector(theme),

        SizedBox(height: 16.h),

        // Type-specific inputs
        _buildTypeSpecificInputs(theme),

        // Validation message
        if (widget.showValidation && widget.validationMessage != null) ...[
          SizedBox(height: 8.h),
          Text(
            widget.validationMessage!,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildScheduleTypeSelector(ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(25.r),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          _buildScheduleTypeTab(
            theme,
            SchedulingType.none,
            'None',
            isFirst: true,
          ),
          _buildScheduleTypeTab(theme, SchedulingType.duration, 'Duration'),
          _buildScheduleTypeTab(
            theme,
            SchedulingType.date,
            'Date',
            isLast: true,
          ),
        ],
      ),
    );
  }

  Widget _buildScheduleTypeTab(
    ThemeData theme,
    SchedulingType type,
    String label, {
    bool isFirst = false,
    bool isLast = false,
  }) {
    final isSelected = widget.selectedType == type;

    return Expanded(
      child: GestureDetector(
        onTap: () => widget.onTypeChanged(type),
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
          decoration: BoxDecoration(
            color: isSelected ? theme.colorScheme.primary : Colors.transparent,
            borderRadius: BorderRadius.horizontal(
              left: isFirst ? Radius.circular(25.r) : Radius.zero,
              right: isLast ? Radius.circular(25.r) : Radius.zero,
            ),
          ),
          child: Text(
            label,
            textAlign: TextAlign.center,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: isSelected
                  ? theme.colorScheme.onPrimary
                  : theme.colorScheme.onSurface,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTypeSpecificInputs(ThemeData theme) {
    switch (widget.selectedType) {
      case SchedulingType.none:
        return const SizedBox.shrink();

      case SchedulingType.duration:
        return _buildDurationInputs(theme);

      case SchedulingType.date:
        return _buildDateInputs(theme);
    }
  }

  Widget _buildDurationInputs(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Duration',
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 8.h),
        _buildDurationSelector(theme),
      ],
    );
  }

  Widget _buildDurationSelector(ThemeData theme) {
    final currentDuration = widget.duration ?? const Duration(days: 1);
    final currentDays = currentDuration.inDays;
    final currentHours = currentDuration.inHours % 24;

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.3),
        ),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: [
          // Quick duration chips
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildDurationChip(theme, const Duration(hours: 6), '1/4 day'),
                SizedBox(width: 8.w),
                _buildDurationChip(theme, const Duration(hours: 12), '1/2 day'),
                SizedBox(width: 8.w),
                _buildDurationChip(theme, const Duration(days: 1), '1 day'),
                SizedBox(width: 8.w),
                _buildDurationChip(theme, const Duration(days: 3), '3 days'),
                SizedBox(width: 8.w),
                _buildDurationChip(theme, const Duration(days: 7), '1 week'),
                SizedBox(width: 8.w),
                _buildDurationChip(theme, const Duration(days: 14), '2 weeks'),
                SizedBox(width: 8.w),
                _buildDurationChip(theme, const Duration(days: 30), '1 month'),
              ],
            ),
          ),

          SizedBox(height: 16.h),

          // Custom duration input
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  initialValue: currentDays.toString(),
                  decoration: const InputDecoration(
                    labelText: 'Days',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    final days = int.tryParse(value) ?? 0;
                    widget.onDurationChanged?.call(
                      Duration(days: days, hours: currentHours),
                    );
                  },
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: TextFormField(
                  initialValue: currentHours.toString(),
                  decoration: const InputDecoration(
                    labelText: 'Hours',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    final hours = int.tryParse(value) ?? 0;
                    widget.onDurationChanged?.call(
                      Duration(days: currentDays, hours: hours),
                    );
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDurationChip(ThemeData theme, Duration duration, String label) {
    final isSelected = widget.duration == duration;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          widget.onDurationChanged?.call(duration);
        }
      },
    );
  }

  Widget _buildDateInputs(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Date Selection',
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 8.h),
        Text(
          'Fill both dates for a range, start date only for specific date, or end date only for deadline',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
        SizedBox(height: 12.h),

        Row(
          children: [
            Expanded(
              child: _buildDateField(
                theme,
                'Start Date',
                widget.startDate,
                widget.onStartDateChanged,
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: _buildDateField(
                theme,
                'End Date',
                widget.endDate,
                widget.onEndDateChanged,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateField(
    ThemeData theme,
    String label,
    DateTime? date,
    ValueChanged<DateTime?>? onChanged,
  ) {
    return OutlinedButton.icon(
      onPressed: onChanged != null
          ? () async {
              final selectedDate = await showDatePicker(
                context: context,
                initialDate:
                    date ?? DateTime.now().add(const Duration(days: 1)),
                firstDate: DateTime.now(),
                lastDate: DateTime.now().add(const Duration(days: 365)),
              );
              if (selectedDate != null) {
                onChanged(selectedDate);
              }
            }
          : null,
      icon: const Icon(Symbols.calendar_today),
      label: Text(
        date?.toString().split(' ')[0] ?? label,
        style: theme.textTheme.bodyMedium,
      ),
    );
  }
}
