import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../enums/study_plan_enums.dart';

/// Widget displaying filter chips for study plan types and categories
class StudyPlanFilterChips extends StatelessWidget {
  /// Currently selected plan type filter
  final StudyPlanType? selectedType;

  /// Currently selected category filter
  final PlanCategoryType? selectedCategory;

  /// Callback when plan type filter changes
  final Function(StudyPlanType? type) onTypeChanged;

  /// Callback when category filter changes
  final Function(PlanCategoryType? category) onCategoryChanged;

  const StudyPlanFilterChips({
    super.key,
    required this.selectedType,
    required this.selectedCategory,
    required this.onTypeChanged,
    required this.onCategoryChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Category filters
        _buildCategoryFilters(context),

        SizedBox(height: 12.h),

        // Type filters
        _buildTypeFilters(context),
      ],
    );
  }

  Widget _buildCategoryFilters(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Categories',
          style: theme.textTheme.labelMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
          ),
        ),
        SizedBox(height: 8.h),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              // "All" category chip
              _buildCategoryChip(
                context: context,
                theme: theme,
                label: 'All Plans',
                icon: Symbols.all_inclusive,
                isSelected: selectedCategory == null,
                onTap: () => onCategoryChanged(null),
              ),

              SizedBox(width: 8.w),

              // Individual category filter chips
              ...PlanCategoryType.values.map((category) {
                return Padding(
                  padding: EdgeInsets.only(right: 8.w),
                  child: _buildCategoryChip(
                    context: context,
                    theme: theme,
                    label: category.displayName,
                    icon: category.icon,
                    isSelected: selectedCategory == category,
                    onTap: () => onCategoryChanged(category),
                    color: category.color,
                  ),
                );
              }),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTypeFilters(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Plan Types',
          style: theme.textTheme.labelMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
          ),
        ),
        SizedBox(height: 8.h),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              // "All" type chip
              _buildTypeChip(
                context: context,
                theme: theme,
                label: 'All Types',
                icon: Symbols.category,
                isSelected: selectedType == null,
                onTap: () => onTypeChanged(null),
              ),

              SizedBox(width: 8.w),

              // Individual type filter chips
              ...StudyPlanType.values.map((type) {
                return Padding(
                  padding: EdgeInsets.only(right: 8.w),
                  child: _buildTypeChip(
                    context: context,
                    theme: theme,
                    label: type.displayName,
                    icon: type.icon,
                    isSelected: selectedType == type,
                    onTap: () => onTypeChanged(type),
                    color: type.color,
                  ),
                );
              }),
            ],
          ),
        ),
      ],
    );
  }

  /// Build individual category filter chip
  Widget _buildCategoryChip({
    required BuildContext context,
    required ThemeData theme,
    required String label,
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
    Color? color,
  }) {
    final effectiveColor = color ?? theme.colorScheme.primary;

    return FilterChip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16.sp,
            color: isSelected
                ? effectiveColor.computeLuminance() > 0.5
                      ? Colors.black
                      : Colors.white
                : effectiveColor,
          ),
          SizedBox(width: 4.w),
          Text(
            label,
            style: theme.textTheme.labelMedium?.copyWith(
              color: isSelected
                  ? effectiveColor.computeLuminance() > 0.5
                        ? Colors.black
                        : Colors.white
                  : theme.colorScheme.onSurface,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ],
      ),
      selected: isSelected,
      onSelected: (_) => onTap(),
      backgroundColor: theme.colorScheme.surface,
      selectedColor: effectiveColor.withValues(alpha: 0.2),
      checkmarkColor: effectiveColor,
      side: BorderSide(
        color: isSelected
            ? effectiveColor
            : theme.colorScheme.outline.withValues(alpha: 0.3),
        width: isSelected ? 1.5.w : 1.w,
      ),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20.r)),
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      visualDensity: VisualDensity.compact,
    );
  }

  /// Build individual type filter chip
  Widget _buildTypeChip({
    required BuildContext context,
    required ThemeData theme,
    required String label,
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
    Color? color,
  }) {
    final effectiveColor = color ?? theme.colorScheme.secondary;

    return FilterChip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16.sp,
            color: isSelected
                ? effectiveColor.computeLuminance() > 0.5
                      ? Colors.black
                      : Colors.white
                : effectiveColor,
          ),
          SizedBox(width: 4.w),
          Text(
            label,
            style: theme.textTheme.labelMedium?.copyWith(
              color: isSelected
                  ? effectiveColor.computeLuminance() > 0.5
                        ? Colors.black
                        : Colors.white
                  : theme.colorScheme.onSurface,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ],
      ),
      selected: isSelected,
      onSelected: (_) => onTap(),
      backgroundColor: theme.colorScheme.surface,
      selectedColor: effectiveColor.withValues(alpha: 0.2),
      checkmarkColor: effectiveColor,
      side: BorderSide(
        color: isSelected
            ? effectiveColor
            : theme.colorScheme.outline.withValues(alpha: 0.3),
        width: isSelected ? 1.5.w : 1.w,
      ),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20.r)),
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      visualDensity: VisualDensity.compact,
    );
  }
}
