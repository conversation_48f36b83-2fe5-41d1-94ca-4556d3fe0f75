import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../enums/study_plan_enums.dart';
import '../../../widgets/study_plan_filter_chips.dart';
import '../../../widgets/study_plan_search_bar.dart';

/// Filter section containing search, category filters, type filters, and view toggle
class FilterSection extends ConsumerWidget {
  /// Current search query
  final String searchQuery;

  /// Callback when search query changes
  final ValueChanged<String> onSearchChanged;

  /// Currently selected category filter
  final PlanCategoryType? selectedCategory;

  /// Callback when category filter changes
  final Function(PlanCategoryType? category) onCategoryChanged;

  /// Currently selected type filter
  final StudyPlanType? selectedType;

  /// Callback when type filter changes
  final Function(StudyPlanType? type) onTypeChanged;

  /// Whether search is active
  final bool isSearchActive;

  /// Callback when search focus changes
  final ValueChanged<bool>? onSearchFocusChanged;

  const FilterSection({
    super.key,
    required this.searchQuery,
    required this.onSearchChanged,
    required this.selectedCategory,
    required this.onCategoryChanged,
    required this.selectedType,
    required this.onTypeChanged,
    this.isSearchActive = false,
    this.onSearchFocusChanged,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Search bar
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: StudyPlanSearchBar(
            searchQuery: searchQuery,
            onSearchChanged: onSearchChanged,
            onSearchClear: () => onSearchChanged(''),
            isActive: isSearchActive,
            onFocusChanged: onSearchFocusChanged,
          ),
        ),

        SizedBox(height: 16.h),

        // Filter chips
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: StudyPlanFilterChips(
            selectedType: selectedType,
            selectedCategory: selectedCategory,
            onTypeChanged: onTypeChanged,
            onCategoryChanged: onCategoryChanged,
          ),
        ),

        // Active filters summary (if any filters are active)
        if (_hasActiveFilters()) ...[
          SizedBox(height: 12.h),
          _buildActiveFiltersSummary(theme),
        ],

        SizedBox(height: 16.h),
      ],
    );
  }

  bool _hasActiveFilters() {
    return searchQuery.isNotEmpty ||
        selectedCategory != null ||
        selectedType != null;
  }

  Widget _buildActiveFiltersSummary(ThemeData theme) {
    final activeFilters = <String>[];

    if (searchQuery.isNotEmpty) {
      activeFilters.add('Search: "$searchQuery"');
    }
    if (selectedCategory != null) {
      activeFilters.add('Category: ${selectedCategory!.displayName}');
    }
    if (selectedType != null) {
      activeFilters.add('Type: ${selectedType!.displayName}');
    }

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: theme.colorScheme.primary.withValues(alpha: 0.3),
          width: 1.w,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Symbols.filter_alt,
            size: 16.sp,
            color: theme.colorScheme.primary,
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              'Active filters: ${activeFilters.join(', ')}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          TextButton(
            onPressed: _clearAllFilters,
            style: TextButton.styleFrom(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            child: Text(
              'Clear',
              style: theme.textTheme.labelSmall?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _clearAllFilters() {
    onSearchChanged('');
    onCategoryChanged(null);
    onTypeChanged(null);
  }
}
