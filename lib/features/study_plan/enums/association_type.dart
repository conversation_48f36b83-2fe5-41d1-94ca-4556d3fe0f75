import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Enum representing different types of associations for study plans
enum AssociationType { classroom, exam, subject, project, none }

extension AssociationTypeExtension on AssociationType {
  /// Display name for the association type
  String get displayName {
    switch (this) {
      case AssociationType.classroom:
        return 'Classroom';
      case AssociationType.exam:
        return 'Exam';
      case AssociationType.subject:
        return 'Subject';
      case AssociationType.project:
        return 'Project';
      case AssociationType.none:
        return 'No Association';
    }
  }

  /// Description for the association type
  String get description {
    switch (this) {
      case AssociationType.classroom:
        return 'Associated with a classroom';
      case AssociationType.exam:
        return 'Associated with an exam';
      case AssociationType.subject:
        return 'Associated with a specific subject';
      case AssociationType.project:
        return 'Associated with a project';
      case AssociationType.none:
        return 'No specific association';
    }
  }

  /// Icon for the association type
  IconData get icon {
    switch (this) {
      case AssociationType.classroom:
        return Symbols.school;
      case AssociationType.exam:
        return Symbols.quiz;
      case AssociationType.subject:
        return Symbols.book;
      case AssociationType.project:
        return Symbols.work;
      case AssociationType.none:
        return Symbols.remove_circle_outline;
    }
  }

  /// Color associated with the association type
  Color get color {
    switch (this) {
      case AssociationType.classroom:
        return Colors.indigo;
      case AssociationType.exam:
        return Colors.orange;
      case AssociationType.subject:
        return Colors.green;
      case AssociationType.project:
        return Colors.purple;
      case AssociationType.none:
        return Colors.grey;
    }
  }

  /// Whether this association type requires an ID
  bool get requiresId {
    switch (this) {
      case AssociationType.classroom:
      case AssociationType.exam:
      case AssociationType.subject:
      case AssociationType.project:
        return true;
      case AssociationType.none:
        return false;
    }
  }

  /// Field name for the associated ID
  String? get idFieldName {
    switch (this) {
      case AssociationType.classroom:
        return 'classroomId';
      case AssociationType.exam:
        return 'examId';
      case AssociationType.subject:
        return 'subjectId';
      case AssociationType.project:
        return 'projectId';
      case AssociationType.none:
        return null;
    }
  }

  /// Whether this association supports multiple instances
  bool get supportsMultiple {
    switch (this) {
      case AssociationType.subject:
        return true; // A plan can be associated with multiple subjects
      case AssociationType.classroom:
      case AssociationType.exam:
      case AssociationType.project:
      case AssociationType.none:
        return false;
    }
  }
}

/// Helper methods for AssociationType
class AssociationTypeHelper {
  /// Get all association types
  static List<AssociationType> get allTypes => AssociationType.values;

  /// Get association types that require IDs
  static List<AssociationType> get typesRequiringIds =>
      AssociationType.values.where((type) => type.requiresId).toList();

  /// Get association types that support multiple instances
  static List<AssociationType> get multipleInstanceTypes =>
      AssociationType.values.where((type) => type.supportsMultiple).toList();

  /// Get association type from string
  static AssociationType? fromString(String value) {
    try {
      return AssociationType.values.firstWhere((type) => type.name == value);
    } catch (e) {
      return null;
    }
  }

  /// Get association type from field name
  static AssociationType? fromFieldName(String fieldName) {
    try {
      return AssociationType.values.firstWhere(
        (type) => type.idFieldName == fieldName,
      );
    } catch (e) {
      return null;
    }
  }

  /// Validate association data
  static bool validateAssociation(AssociationType type, String? associatedId) {
    if (type.requiresId) {
      return associatedId != null && associatedId.isNotEmpty;
    }
    return true; // No validation needed for types that don't require IDs
  }
}
