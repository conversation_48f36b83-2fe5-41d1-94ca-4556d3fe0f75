import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Enum representing the scope of study plan assignment
enum AssignmentScope { individual, group, classroom, organization }

extension AssignmentScopeExtension on AssignmentScope {
  /// Display name for the assignment scope
  String get displayName {
    switch (this) {
      case AssignmentScope.individual:
        return 'Individual';
      case AssignmentScope.group:
        return 'Group';
      case AssignmentScope.classroom:
        return 'Classroom';
      case AssignmentScope.organization:
        return 'Organization';
    }
  }

  /// Description for the assignment scope
  String get description {
    switch (this) {
      case AssignmentScope.individual:
        return 'Assigned to a single person';
      case AssignmentScope.group:
        return 'Assigned to a specific group of people';
      case AssignmentScope.classroom:
        return 'Assigned to an entire classroom';
      case AssignmentScope.organization:
        return 'Assigned to the entire organization';
    }
  }

  /// Icon for the assignment scope
  IconData get icon {
    switch (this) {
      case AssignmentScope.individual:
        return Symbols.person;
      case AssignmentScope.group:
        return Symbols.group;
      case AssignmentScope.classroom:
        return Symbols.school;
      case AssignmentScope.organization:
        return Symbols.business;
    }
  }

  /// Color associated with the assignment scope
  Color get color {
    switch (this) {
      case AssignmentScope.individual:
        return Colors.blue;
      case AssignmentScope.group:
        return Colors.green;
      case AssignmentScope.classroom:
        return Colors.orange;
      case AssignmentScope.organization:
        return Colors.purple;
    }
  }

  /// Numeric value for scope hierarchy (higher = broader scope)
  int get scopeLevel {
    switch (this) {
      case AssignmentScope.individual:
        return 1;
      case AssignmentScope.group:
        return 2;
      case AssignmentScope.classroom:
        return 3;
      case AssignmentScope.organization:
        return 4;
    }
  }

  /// Whether this scope allows collaboration
  bool get allowsCollaboration {
    switch (this) {
      case AssignmentScope.individual:
        return false;
      case AssignmentScope.group:
      case AssignmentScope.classroom:
      case AssignmentScope.organization:
        return true;
    }
  }

  /// Whether this scope requires approval for assignment
  bool get requiresApproval {
    switch (this) {
      case AssignmentScope.individual:
      case AssignmentScope.group:
        return false;
      case AssignmentScope.classroom:
      case AssignmentScope.organization:
        return true;
    }
  }

  /// Whether this scope supports progress tracking
  bool get supportsProgressTracking {
    return true; // All scopes support progress tracking
  }

  /// Whether this scope allows individual customization
  bool get allowsIndividualCustomization {
    switch (this) {
      case AssignmentScope.individual:
        return true;
      case AssignmentScope.group:
        return true; // Limited customization
      case AssignmentScope.classroom:
      case AssignmentScope.organization:
        return false; // Standardized for consistency
    }
  }

  /// Whether this scope supports notifications
  bool get supportsNotifications {
    return true; // All scopes support notifications
  }

  /// Whether this scope is broader than another scope
  bool isBroaderThan(AssignmentScope other) {
    return scopeLevel > other.scopeLevel;
  }

  /// Whether this scope is narrower than another scope
  bool isNarrowerThan(AssignmentScope other) {
    return scopeLevel < other.scopeLevel;
  }

  /// Maximum recommended number of assignees for this scope
  int get maxRecommendedAssignees {
    switch (this) {
      case AssignmentScope.individual:
        return 1;
      case AssignmentScope.group:
        return 10;
      case AssignmentScope.classroom:
        return 50;
      case AssignmentScope.organization:
        return 1000; // No practical limit
    }
  }

  /// Minimum required permissions level to assign at this scope
  String get requiredPermissionLevel {
    switch (this) {
      case AssignmentScope.individual:
        return 'basic';
      case AssignmentScope.group:
        return 'group_leader';
      case AssignmentScope.classroom:
        return 'teacher';
      case AssignmentScope.organization:
        return 'admin';
    }
  }

  /// Default visibility level for this scope
  String get defaultVisibility {
    switch (this) {
      case AssignmentScope.individual:
        return 'private';
      case AssignmentScope.group:
        return 'group';
      case AssignmentScope.classroom:
        return 'classroom';
      case AssignmentScope.organization:
        return 'organization';
    }
  }

  /// Whether this scope supports deadline flexibility
  bool get supportsDeadlineFlexibility {
    switch (this) {
      case AssignmentScope.individual:
        return true;
      case AssignmentScope.group:
        return true;
      case AssignmentScope.classroom:
        return false; // Standardized deadlines
      case AssignmentScope.organization:
        return false; // Standardized deadlines
    }
  }
}

/// Helper methods for AssignmentScope
class AssignmentScopeHelper {
  /// Get all assignment scopes
  static List<AssignmentScope> get allScopes => AssignmentScope.values;

  /// Get scopes sorted by level (narrow to broad)
  static List<AssignmentScope> get scopesByLevel {
    final scopes = List<AssignmentScope>.from(AssignmentScope.values);
    scopes.sort((a, b) => a.scopeLevel.compareTo(b.scopeLevel));
    return scopes;
  }

  /// Get scopes that allow collaboration
  static List<AssignmentScope> get collaborativeScopes => AssignmentScope.values
      .where((scope) => scope.allowsCollaboration)
      .toList();

  /// Get scopes that require approval
  static List<AssignmentScope> get approvalRequiredScopes =>
      AssignmentScope.values.where((scope) => scope.requiresApproval).toList();

  /// Get scopes that allow individual customization
  static List<AssignmentScope> get customizableScopes => AssignmentScope.values
      .where((scope) => scope.allowsIndividualCustomization)
      .toList();

  /// Get scopes that support deadline flexibility
  static List<AssignmentScope> get flexibleDeadlineScopes => AssignmentScope
      .values
      .where((scope) => scope.supportsDeadlineFlexibility)
      .toList();

  /// Get assignment scope from string
  static AssignmentScope? fromString(String value) {
    try {
      return AssignmentScope.values.firstWhere((scope) => scope.name == value);
    } catch (e) {
      return null;
    }
  }

  /// Get assignment scope from scope level
  static AssignmentScope? fromScopeLevel(int level) {
    try {
      return AssignmentScope.values.firstWhere(
        (scope) => scope.scopeLevel == level,
      );
    } catch (e) {
      return null;
    }
  }

  /// Get appropriate scope based on number of assignees
  static AssignmentScope getRecommendedScopeForAssigneeCount(
    int assigneeCount,
  ) {
    if (assigneeCount <= 1) {
      return AssignmentScope.individual;
    } else if (assigneeCount <= 10) {
      return AssignmentScope.group;
    } else if (assigneeCount <= 50) {
      return AssignmentScope.classroom;
    } else {
      return AssignmentScope.organization;
    }
  }

  /// Check if a user can assign at a specific scope
  static bool canUserAssignAtScope({
    required String userPermissionLevel,
    required AssignmentScope scope,
  }) {
    final requiredLevel = scope.requiredPermissionLevel;

    // Simple permission hierarchy check
    const permissionHierarchy = {
      'basic': 1,
      'group_leader': 2,
      'teacher': 3,
      'admin': 4,
    };

    final userLevel = permissionHierarchy[userPermissionLevel] ?? 0;
    final requiredLevelValue = permissionHierarchy[requiredLevel] ?? 0;

    return userLevel >= requiredLevelValue;
  }

  /// Get scopes available to a user based on their permission level
  static List<AssignmentScope> getAvailableScopesForUser(
    String userPermissionLevel,
  ) {
    return AssignmentScope.values
        .where(
          (scope) => canUserAssignAtScope(
            userPermissionLevel: userPermissionLevel,
            scope: scope,
          ),
        )
        .toList();
  }

  /// Filter scopes by maximum assignee count
  static List<AssignmentScope> filterByMaxAssignees(int maxAssignees) {
    return AssignmentScope.values
        .where((scope) => scope.maxRecommendedAssignees >= maxAssignees)
        .toList();
  }
}
