import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Enum representing the 4-step creation process for study plans
enum PlanCreationStep { basicDetails, tasksAndGoals, scheduling, preview }

extension PlanCreationStepExtension on PlanCreationStep {
  /// Display name for the creation step
  String get displayName {
    switch (this) {
      case PlanCreationStep.basicDetails:
        return 'Basic Details';
      case PlanCreationStep.tasksAndGoals:
        return 'Tasks & Goals';
      case PlanCreationStep.scheduling:
        return 'Scheduling';
      case PlanCreationStep.preview:
        return 'Preview';
    }
  }

  /// Description for the creation step
  String get description {
    switch (this) {
      case PlanCreationStep.basicDetails:
        return 'Name, Description, Type, Creating for?, Mandatory or not, association (classroom/exam), Can be edited as template?';
      case PlanCreationStep.scheduling:
        return 'Set timeline, duration, deadlines, and schedule preferences (Optional)';
      case PlanCreationStep.tasksAndGoals:
        return 'Define subjects, tasks, goals, sections, and sub-sections';
      case PlanCreationStep.preview:
        return 'Overview and finalization of the plan';
    }
  }

  /// Short description for the creation step
  String get shortDescription {
    switch (this) {
      case PlanCreationStep.basicDetails:
        return 'Set up plan basics and metadata';
      case PlanCreationStep.scheduling:
        return 'Configure timeline and deadlines';
      case PlanCreationStep.tasksAndGoals:
        return 'Create hierarchical content structure';
      case PlanCreationStep.preview:
        return 'Review and finalize plan';
    }
  }

  /// Icon for the creation step
  IconData get icon {
    switch (this) {
      case PlanCreationStep.basicDetails:
        return Symbols.info;
      case PlanCreationStep.tasksAndGoals:
        return Symbols.list_alt;
      case PlanCreationStep.scheduling:
        return Symbols.schedule;
      case PlanCreationStep.preview:
        return Symbols.preview;
    }
  }

  /// Step number (1-based)
  int get stepNumber {
    switch (this) {
      case PlanCreationStep.basicDetails:
        return 1;
      case PlanCreationStep.tasksAndGoals:
        return 2;
      case PlanCreationStep.scheduling:
        return 3;
      case PlanCreationStep.preview:
        return 4;
    }
  }

  /// Page index (0-based) for PageView navigation
  int get pageIndex {
    switch (this) {
      case PlanCreationStep.basicDetails:
        return 0;
      case PlanCreationStep.tasksAndGoals:
        return 1;
      case PlanCreationStep.scheduling:
        return 2;
      case PlanCreationStep.preview:
        return 3;
    }
  }

  /// Whether this step is optional
  bool get isOptional {
    switch (this) {
      case PlanCreationStep.basicDetails:
      case PlanCreationStep.tasksAndGoals:
      case PlanCreationStep.preview:
        return false;
      case PlanCreationStep.scheduling:
        return true; // Scheduling is optional
    }
  }

  /// Whether this step can be skipped
  bool get canBeSkipped {
    return isOptional;
  }

  /// Get the next step in the creation process
  PlanCreationStep? get nextStep {
    switch (this) {
      case PlanCreationStep.basicDetails:
        return PlanCreationStep.tasksAndGoals;
      case PlanCreationStep.tasksAndGoals:
        return PlanCreationStep.scheduling;
      case PlanCreationStep.scheduling:
        return PlanCreationStep.preview;
      case PlanCreationStep.preview:
        return null; // Last step
    }
  }

  /// Get the previous step in the creation process
  PlanCreationStep? get previousStep {
    switch (this) {
      case PlanCreationStep.basicDetails:
        return null; // First step
      case PlanCreationStep.tasksAndGoals:
        return PlanCreationStep.basicDetails;
      case PlanCreationStep.scheduling:
        return PlanCreationStep.tasksAndGoals;
      case PlanCreationStep.preview:
        return PlanCreationStep.scheduling;
    }
  }

  /// Progress percentage for this step (0.0 to 1.0)
  double get progressPercentage {
    switch (this) {
      case PlanCreationStep.basicDetails:
        return 0.25;
      case PlanCreationStep.tasksAndGoals:
        return 0.50;
      case PlanCreationStep.scheduling:
        return 0.75;
      case PlanCreationStep.preview:
        return 1.0;
    }
  }
}

/// Helper methods for PlanCreationStep
class PlanCreationStepHelper {
  /// Get all creation steps
  static List<PlanCreationStep> get allSteps => PlanCreationStep.values;

  /// Get required steps (non-optional)
  static List<PlanCreationStep> get requiredSteps =>
      PlanCreationStep.values.where((step) => !step.isOptional).toList();

  /// Get optional steps
  static List<PlanCreationStep> get optionalSteps =>
      PlanCreationStep.values.where((step) => step.isOptional).toList();

  /// Get step by number (1-based)
  static PlanCreationStep? getStepByNumber(int stepNumber) {
    try {
      return PlanCreationStep.values.firstWhere(
        (step) => step.stepNumber == stepNumber,
      );
    } catch (e) {
      return null;
    }
  }

  /// Get creation step from string
  static PlanCreationStep? fromString(String value) {
    try {
      return PlanCreationStep.values.firstWhere((step) => step.name == value);
    } catch (e) {
      return null;
    }
  }

  /// Get step by page index (0-based)
  static PlanCreationStep? getStepByPageIndex(int pageIndex) {
    try {
      return PlanCreationStep.values.firstWhere(
        (step) => step.pageIndex == pageIndex,
      );
    } catch (e) {
      return null;
    }
  }

  /// Calculate overall progress based on completed steps
  static double calculateProgress(List<PlanCreationStep> completedSteps) {
    if (completedSteps.isEmpty) return 0.0;

    final maxStep = completedSteps.reduce(
      (a, b) => a.stepNumber > b.stepNumber ? a : b,
    );

    return maxStep.progressPercentage;
  }

  /// Get next required step after given step
  static PlanCreationStep? getNextRequiredStep(PlanCreationStep currentStep) {
    PlanCreationStep? next = currentStep.nextStep;
    while (next != null && next.isOptional) {
      next = next.nextStep;
    }
    return next;
  }
}
