import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Enum representing different categories for organizing study plans in list view
enum PlanCategoryType {
  createdByUser,
  assignedToUser,
  classroomBased,
  templates,
}

extension PlanCategoryTypeExtension on PlanCategoryType {
  /// Display name for the plan category
  String get displayName {
    switch (this) {
      case PlanCategoryType.createdByUser:
        return 'My Plans';
      case PlanCategoryType.assignedToUser:
        return 'Assigned to Me';
      case PlanCategoryType.classroomBased:
        return 'Classroom Plans';
      case PlanCategoryType.templates:
        return 'Templates';
    }
  }

  /// Description for the plan category
  String get description {
    switch (this) {
      case PlanCategoryType.createdByUser:
        return 'Plans created by the current user';
      case PlanCategoryType.assignedToUser:
        return 'Plans assigned/shared with the user';
      case PlanCategoryType.classroomBased:
        return 'Plans from classrooms user is member of';
      case PlanCategoryType.templates:
        return 'Available templates';
    }
  }

  /// Icon for the plan category
  IconData get icon {
    switch (this) {
      case PlanCategoryType.createdByUser:
        return Symbols.person;
      case PlanCategoryType.assignedToUser:
        return Symbols.assignment_ind;
      case PlanCategoryType.classroomBased:
        return Symbols.school;
      case PlanCategoryType.templates:
        return Symbols.content_copy;
    }
  }

  /// Color associated with the plan category
  Color get color {
    switch (this) {
      case PlanCategoryType.createdByUser:
        return Colors.blue;
      case PlanCategoryType.assignedToUser:
        return Colors.orange;
      case PlanCategoryType.classroomBased:
        return Colors.green;
      case PlanCategoryType.templates:
        return Colors.purple;
    }
  }

  /// Whether this category allows creating new plans
  bool get allowsCreation {
    switch (this) {
      case PlanCategoryType.createdByUser:
      case PlanCategoryType.templates:
        return true;
      case PlanCategoryType.assignedToUser:
      case PlanCategoryType.classroomBased:
        return false;
    }
  }

  /// Whether this category allows editing plans
  bool get allowsEditing {
    switch (this) {
      case PlanCategoryType.createdByUser:
        return true;
      case PlanCategoryType.assignedToUser:
      case PlanCategoryType.classroomBased:
      case PlanCategoryType.templates:
        return false; // Depends on permissions
    }
  }

  /// Whether this category shows plan progress
  bool get showsProgress {
    switch (this) {
      case PlanCategoryType.createdByUser:
      case PlanCategoryType.assignedToUser:
      case PlanCategoryType.classroomBased:
        return true;
      case PlanCategoryType.templates:
        return false; // Templates don't have progress
    }
  }

  /// Whether this category supports filtering
  bool get supportsFiltering {
    return true; // All categories support filtering
  }

  /// Default sort order for this category
  String get defaultSortOrder {
    switch (this) {
      case PlanCategoryType.createdByUser:
        return 'lastModified';
      case PlanCategoryType.assignedToUser:
        return 'dueDate';
      case PlanCategoryType.classroomBased:
        return 'createdAt';
      case PlanCategoryType.templates:
        return 'popularity';
    }
  }

  /// Priority order for displaying categories (lower = higher priority)
  int get displayPriority {
    switch (this) {
      case PlanCategoryType.createdByUser:
        return 1;
      case PlanCategoryType.assignedToUser:
        return 2;
      case PlanCategoryType.classroomBased:
        return 3;
      case PlanCategoryType.templates:
        return 4;
    }
  }
}

/// Helper methods for PlanCategoryType
class PlanCategoryTypeHelper {
  /// Get all plan categories
  static List<PlanCategoryType> get allCategories => PlanCategoryType.values;

  /// Get categories that allow creation
  static List<PlanCategoryType> get creationAllowedCategories =>
      PlanCategoryType.values
          .where((category) => category.allowsCreation)
          .toList();

  /// Get categories that allow editing
  static List<PlanCategoryType> get editingAllowedCategories => PlanCategoryType
      .values
      .where((category) => category.allowsEditing)
      .toList();

  /// Get categories that show progress
  static List<PlanCategoryType> get progressShowingCategories =>
      PlanCategoryType.values
          .where((category) => category.showsProgress)
          .toList();

  /// Get categories sorted by display priority
  static List<PlanCategoryType> get categoriesByPriority {
    final categories = List<PlanCategoryType>.from(PlanCategoryType.values);
    categories.sort((a, b) => a.displayPriority.compareTo(b.displayPriority));
    return categories;
  }

  /// Get plan category from string
  static PlanCategoryType? fromString(String value) {
    try {
      return PlanCategoryType.values.firstWhere(
        (category) => category.name == value,
      );
    } catch (e) {
      return null;
    }
  }

  /// Get appropriate category for a plan based on user relationship
  static PlanCategoryType getCategoryForPlan({
    required String currentUserId,
    required String planCreatorId,
    required List<String> assignedUserIds,
    required String? classroomId,
    required bool isTemplate,
  }) {
    if (isTemplate) {
      return PlanCategoryType.templates;
    }

    if (planCreatorId == currentUserId) {
      return PlanCategoryType.createdByUser;
    }

    if (assignedUserIds.contains(currentUserId)) {
      return PlanCategoryType.assignedToUser;
    }

    if (classroomId != null) {
      return PlanCategoryType.classroomBased;
    }

    // Default fallback
    return PlanCategoryType.assignedToUser;
  }
}
