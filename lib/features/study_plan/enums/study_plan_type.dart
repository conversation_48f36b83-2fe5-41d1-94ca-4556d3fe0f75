import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Enum representing different types of study plans
enum StudyPlanType {
  shortTerm,
  longTerm,
  examPreparation,
  revision,
  classPlan,
  selfStudy,
  projectBased,
  general,
}

extension StudyPlanTypeExtension on StudyPlanType {
  /// Display name for the study plan type
  String get displayName {
    switch (this) {
      case StudyPlanType.shortTerm:
        return 'Short Term';
      case StudyPlanType.longTerm:
        return 'Long Term';
      case StudyPlanType.examPreparation:
        return 'Exam Preparation';
      case StudyPlanType.revision:
        return 'Revision';
      case StudyPlanType.classPlan:
        return 'Class Plan';
      case StudyPlanType.selfStudy:
        return 'Self Study';
      case StudyPlanType.projectBased:
        return 'Project Based';
      case StudyPlanType.general:
        return 'General';
    }
  }

  /// Description for the study plan type
  String get description {
    switch (this) {
      case StudyPlanType.shortTerm:
        return 'Daily/weekly focused plans (1-4 weeks)';
      case StudyPlanType.longTerm:
        return 'Semester/yearly academic plans (3-12 months)';
      case StudyPlanType.examPreparation:
        return 'Targeted exam-focused plans';
      case StudyPlanType.revision:
        return 'Review and reinforcement plans';
      case StudyPlanType.classPlan:
        return 'Teacher-created classroom plans';
      case StudyPlanType.selfStudy:
        return 'Personal learning plans';
      case StudyPlanType.projectBased:
        return 'Project completion plans';
      case StudyPlanType.general:
        return 'General plan for unspecified cases';
    }
  }

  /// Icon for the study plan type
  IconData get icon {
    switch (this) {
      case StudyPlanType.shortTerm:
        return Symbols.schedule;
      case StudyPlanType.longTerm:
        return Symbols.calendar_view_month;
      case StudyPlanType.examPreparation:
        return Symbols.quiz;
      case StudyPlanType.revision:
        return Symbols.refresh;
      case StudyPlanType.classPlan:
        return Symbols.school;
      case StudyPlanType.selfStudy:
        return Symbols.person;
      case StudyPlanType.projectBased:
        return Symbols.work;
      case StudyPlanType.general:
        return Symbols.description;
    }
  }

  /// Color associated with the study plan type
  Color get color {
    switch (this) {
      case StudyPlanType.shortTerm:
        return Colors.blue;
      case StudyPlanType.longTerm:
        return Colors.green;
      case StudyPlanType.examPreparation:
        return Colors.orange;
      case StudyPlanType.revision:
        return Colors.purple;
      case StudyPlanType.classPlan:
        return Colors.indigo;
      case StudyPlanType.selfStudy:
        return Colors.teal;
      case StudyPlanType.projectBased:
        return Colors.brown;
      case StudyPlanType.general:
        return Colors.grey;
    }
  }

  /// Typical duration range for the plan type
  String get typicalDuration {
    switch (this) {
      case StudyPlanType.shortTerm:
        return '1-4 weeks';
      case StudyPlanType.longTerm:
        return '3-12 months';
      case StudyPlanType.examPreparation:
        return '2-8 weeks';
      case StudyPlanType.revision:
        return '1-6 weeks';
      case StudyPlanType.classPlan:
        return '1 semester';
      case StudyPlanType.selfStudy:
        return 'Flexible';
      case StudyPlanType.projectBased:
        return '2-16 weeks';
      case StudyPlanType.general:
        return 'Variable';
    }
  }

  /// Whether this plan type typically requires scheduling
  bool get requiresScheduling {
    switch (this) {
      case StudyPlanType.shortTerm:
      case StudyPlanType.examPreparation:
      case StudyPlanType.classPlan:
        return true;
      case StudyPlanType.longTerm:
      case StudyPlanType.revision:
      case StudyPlanType.selfStudy:
      case StudyPlanType.projectBased:
      case StudyPlanType.general:
        return false; // Optional scheduling
    }
  }

  /// Whether this plan type supports templates
  bool get supportsTemplates {
    switch (this) {
      case StudyPlanType.classPlan:
      case StudyPlanType.examPreparation:
      case StudyPlanType.revision:
      case StudyPlanType.general:
        return true;
      case StudyPlanType.shortTerm:
      case StudyPlanType.longTerm:
      case StudyPlanType.selfStudy:
      case StudyPlanType.projectBased:
        return false;
    }
  }
}

/// Helper methods for StudyPlanType
class StudyPlanTypeHelper {
  /// Get all study plan types
  static List<StudyPlanType> get allTypes => StudyPlanType.values;

  /// Get study plan types suitable for templates
  static List<StudyPlanType> get templateSuitableTypes =>
      StudyPlanType.values.where((type) => type.supportsTemplates).toList();

  /// Get study plan types that require scheduling
  static List<StudyPlanType> get schedulingRequiredTypes =>
      StudyPlanType.values.where((type) => type.requiresScheduling).toList();

  /// Get study plan type from string
  static StudyPlanType? fromString(String value) {
    try {
      return StudyPlanType.values.firstWhere((type) => type.name == value);
    } catch (e) {
      return null;
    }
  }
}
