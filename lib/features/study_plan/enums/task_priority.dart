import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Enum representing task priority levels within study plans
enum TaskPriority { low, normal, high, urgent }

extension TaskPriorityExtension on TaskPriority {
  /// Display name for the task priority
  String get displayName {
    switch (this) {
      case TaskPriority.low:
        return 'Low';
      case TaskPriority.normal:
        return 'Normal';
      case TaskPriority.high:
        return 'High';
      case TaskPriority.urgent:
        return 'Urgent';
    }
  }

  /// Description for the task priority
  String get description {
    switch (this) {
      case TaskPriority.low:
        return 'Low priority - can be done when time permits';
      case TaskPriority.normal:
        return 'Normal priority - standard importance';
      case TaskPriority.high:
        return 'High priority - important and should be prioritized';
      case TaskPriority.urgent:
        return 'Urgent priority - requires immediate attention';
    }
  }

  /// Icon for the task priority
  IconData get icon {
    switch (this) {
      case TaskPriority.low:
        return Symbols.keyboard_arrow_down;
      case TaskPriority.normal:
        return Symbols.remove;
      case TaskPriority.high:
        return Symbols.keyboard_arrow_up;
      case TaskPriority.urgent:
        return Symbols.priority_high;
    }
  }

  /// Color associated with the task priority
  Color get color {
    switch (this) {
      case TaskPriority.low:
        return Colors.green;
      case TaskPriority.normal:
        return Colors.blue;
      case TaskPriority.high:
        return Colors.orange;
      case TaskPriority.urgent:
        return Colors.red;
    }
  }

  /// Numeric value for sorting (higher = more urgent)
  int get value {
    switch (this) {
      case TaskPriority.low:
        return 1;
      case TaskPriority.normal:
        return 2;
      case TaskPriority.high:
        return 3;
      case TaskPriority.urgent:
        return 4;
    }
  }

  /// Weight for priority calculations (higher = more important)
  double get weight {
    switch (this) {
      case TaskPriority.low:
        return 0.5;
      case TaskPriority.normal:
        return 1.0;
      case TaskPriority.high:
        return 1.5;
      case TaskPriority.urgent:
        return 2.0;
    }
  }

  /// Whether this priority requires immediate attention
  bool get requiresImmediateAttention {
    return this == TaskPriority.urgent;
  }

  /// Whether this priority is above normal
  bool get isAboveNormal {
    return value > TaskPriority.normal.value;
  }

  /// Whether this priority is below normal
  bool get isBelowNormal {
    return value < TaskPriority.normal.value;
  }

  /// Whether this priority is higher than another priority
  bool isHigherThan(TaskPriority other) {
    return value > other.value;
  }

  /// Whether this priority is lower than another priority
  bool isLowerThan(TaskPriority other) {
    return value < other.value;
  }

  /// Get recommended time allocation multiplier based on priority
  double get timeAllocationMultiplier {
    switch (this) {
      case TaskPriority.low:
        return 0.8; // 20% less time
      case TaskPriority.normal:
        return 1.0; // Standard time
      case TaskPriority.high:
        return 1.3; // 30% more time
      case TaskPriority.urgent:
        return 1.5; // 50% more time
    }
  }

  /// Get recommended notification frequency based on priority
  Duration get recommendedNotificationFrequency {
    switch (this) {
      case TaskPriority.low:
        return const Duration(days: 3); // Every 3 days
      case TaskPriority.normal:
        return const Duration(days: 1); // Daily
      case TaskPriority.high:
        return const Duration(hours: 12); // Twice daily
      case TaskPriority.urgent:
        return const Duration(hours: 4); // Every 4 hours
    }
  }

  /// Get recommended deadline buffer based on priority
  Duration get recommendedDeadlineBuffer {
    switch (this) {
      case TaskPriority.low:
        return const Duration(days: 3); // 3 days buffer
      case TaskPriority.normal:
        return const Duration(days: 1); // 1 day buffer
      case TaskPriority.high:
        return const Duration(hours: 12); // 12 hours buffer
      case TaskPriority.urgent:
        return const Duration(hours: 4); // 4 hours buffer
    }
  }

  /// Whether this priority should be highlighted in UI
  bool get shouldHighlight {
    return isAboveNormal;
  }

  /// Whether this priority should show warning indicators
  bool get shouldShowWarning {
    return this == TaskPriority.urgent;
  }

  /// Get sorting order for task lists (urgent first)
  int get sortOrder {
    switch (this) {
      case TaskPriority.urgent:
        return 1;
      case TaskPriority.high:
        return 2;
      case TaskPriority.normal:
        return 3;
      case TaskPriority.low:
        return 4;
    }
  }
}

/// Helper methods for TaskPriority
class TaskPriorityHelper {
  /// Get all task priorities
  static List<TaskPriority> get allPriorities => TaskPriority.values;

  /// Get priorities sorted by value (low to high)
  static List<TaskPriority> get prioritiesByValue {
    final priorities = List<TaskPriority>.from(TaskPriority.values);
    priorities.sort((a, b) => a.value.compareTo(b.value));
    return priorities;
  }

  /// Get priorities sorted by urgency (urgent to low)
  static List<TaskPriority> get prioritiesByUrgency {
    final priorities = List<TaskPriority>.from(TaskPriority.values);
    priorities.sort((a, b) => a.sortOrder.compareTo(b.sortOrder));
    return priorities;
  }

  /// Get high priority levels (high and urgent)
  static List<TaskPriority> get highPriorities => [
    TaskPriority.high,
    TaskPriority.urgent,
  ];

  /// Get low priority levels (low and normal)
  static List<TaskPriority> get lowPriorities => [
    TaskPriority.low,
    TaskPriority.normal,
  ];

  /// Get priorities that require immediate attention
  static List<TaskPriority> get urgentPriorities => TaskPriority.values
      .where((priority) => priority.requiresImmediateAttention)
      .toList();

  /// Get priorities that should be highlighted
  static List<TaskPriority> get highlightedPriorities => TaskPriority.values
      .where((priority) => priority.shouldHighlight)
      .toList();

  /// Get priorities that should show warnings
  static List<TaskPriority> get warningPriorities => TaskPriority.values
      .where((priority) => priority.shouldShowWarning)
      .toList();

  /// Get task priority from string
  static TaskPriority? fromString(String value) {
    try {
      return TaskPriority.values.firstWhere(
        (priority) => priority.name == value,
      );
    } catch (e) {
      return null;
    }
  }

  /// Get task priority from numeric value
  static TaskPriority? fromValue(int value) {
    try {
      return TaskPriority.values.firstWhere(
        (priority) => priority.value == value,
      );
    } catch (e) {
      return null;
    }
  }

  /// Calculate average priority from a list of priorities
  static TaskPriority calculateAveragePriority(List<TaskPriority> priorities) {
    if (priorities.isEmpty) return TaskPriority.normal;

    final totalValue = priorities.fold<int>(
      0,
      (sum, priority) => sum + priority.value,
    );
    final averageValue = (totalValue / priorities.length).round();

    return fromValue(averageValue.clamp(1, 4)) ?? TaskPriority.normal;
  }

  /// Get recommended priority based on deadline urgency
  static TaskPriority getRecommendedPriorityForDeadline(DateTime deadline) {
    final now = DateTime.now();
    final timeUntilDeadline = deadline.difference(now);

    if (timeUntilDeadline.inHours <= 12) {
      return TaskPriority.urgent;
    } else if (timeUntilDeadline.inDays <= 1) {
      return TaskPriority.high;
    } else if (timeUntilDeadline.inDays <= 3) {
      return TaskPriority.normal;
    } else {
      return TaskPriority.low;
    }
  }

  /// Get recommended priority based on task importance and urgency
  static TaskPriority getRecommendedPriority({
    required bool isImportant,
    required bool isUrgent,
  }) {
    if (isImportant && isUrgent) {
      return TaskPriority.urgent;
    } else if (isImportant && !isUrgent) {
      return TaskPriority.high;
    } else if (!isImportant && isUrgent) {
      return TaskPriority.normal;
    } else {
      return TaskPriority.low;
    }
  }

  /// Filter priorities by minimum level
  static List<TaskPriority> filterByMinimumLevel(TaskPriority minimumLevel) {
    return TaskPriority.values
        .where((priority) => priority.value >= minimumLevel.value)
        .toList();
  }

  /// Filter priorities by maximum level
  static List<TaskPriority> filterByMaximumLevel(TaskPriority maximumLevel) {
    return TaskPriority.values
        .where((priority) => priority.value <= maximumLevel.value)
        .toList();
  }

  /// Get priority distribution statistics
  static Map<TaskPriority, int> getPriorityDistribution(
    List<TaskPriority> priorities,
  ) {
    final distribution = <TaskPriority, int>{};

    for (final priority in TaskPriority.values) {
      distribution[priority] = priorities.where((p) => p == priority).length;
    }

    return distribution;
  }

  /// Calculate weighted priority score for a list of tasks
  static double calculateWeightedPriorityScore(List<TaskPriority> priorities) {
    if (priorities.isEmpty) return 0.0;

    final totalWeight = priorities.fold<double>(
      0.0,
      (sum, priority) => sum + priority.weight,
    );
    return totalWeight / priorities.length;
  }
}
