import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Enum representing different status states of a study plan
enum StudyPlanStatus { draft, active, paused, completed, cancelled, archived }

extension StudyPlanStatusExtension on StudyPlanStatus {
  /// Display name for the study plan status
  String get displayName {
    switch (this) {
      case StudyPlanStatus.draft:
        return 'Draft';
      case StudyPlanStatus.active:
        return 'Active';
      case StudyPlanStatus.paused:
        return 'Paused';
      case StudyPlanStatus.completed:
        return 'Completed';
      case StudyPlanStatus.cancelled:
        return 'Cancelled';
      case StudyPlanStatus.archived:
        return 'Archived';
    }
  }

  /// Description for the study plan status
  String get description {
    switch (this) {
      case StudyPlanStatus.draft:
        return 'Plan being created';
      case StudyPlanStatus.active:
        return 'Currently running plan';
      case StudyPlanStatus.paused:
        return 'Temporarily stopped';
      case StudyPlanStatus.completed:
        return 'Successfully finished';
      case StudyPlanStatus.cancelled:
        return 'Discontinued plan';
      case StudyPlanStatus.archived:
        return 'Stored for reference';
    }
  }

  /// Icon for the study plan status
  IconData get icon {
    switch (this) {
      case StudyPlanStatus.draft:
        return Symbols.edit_note;
      case StudyPlanStatus.active:
        return Symbols.play_circle;
      case StudyPlanStatus.paused:
        return Symbols.pause_circle;
      case StudyPlanStatus.completed:
        return Symbols.check_circle;
      case StudyPlanStatus.cancelled:
        return Symbols.cancel;
      case StudyPlanStatus.archived:
        return Symbols.archive;
    }
  }

  /// Color associated with the study plan status
  Color get color {
    switch (this) {
      case StudyPlanStatus.draft:
        return Colors.grey;
      case StudyPlanStatus.active:
        return Colors.green;
      case StudyPlanStatus.paused:
        return Colors.orange;
      case StudyPlanStatus.completed:
        return Colors.blue;
      case StudyPlanStatus.cancelled:
        return Colors.red;
      case StudyPlanStatus.archived:
        return Colors.blueGrey;
    }
  }

  /// Whether the plan can be edited in this status
  bool get canBeEdited {
    switch (this) {
      case StudyPlanStatus.draft:
      case StudyPlanStatus.active:
      case StudyPlanStatus.paused:
        return true;
      case StudyPlanStatus.completed:
      case StudyPlanStatus.cancelled:
      case StudyPlanStatus.archived:
        return false;
    }
  }

  /// Whether the plan can be started/resumed in this status
  bool get canBeStarted {
    switch (this) {
      case StudyPlanStatus.draft:
      case StudyPlanStatus.paused:
        return true;
      case StudyPlanStatus.active:
      case StudyPlanStatus.completed:
      case StudyPlanStatus.cancelled:
      case StudyPlanStatus.archived:
        return false;
    }
  }

  /// Whether the plan can be paused in this status
  bool get canBePaused {
    switch (this) {
      case StudyPlanStatus.active:
        return true;
      case StudyPlanStatus.draft:
      case StudyPlanStatus.paused:
      case StudyPlanStatus.completed:
      case StudyPlanStatus.cancelled:
      case StudyPlanStatus.archived:
        return false;
    }
  }

  /// Whether the plan can be completed in this status
  bool get canBeCompleted {
    switch (this) {
      case StudyPlanStatus.active:
      case StudyPlanStatus.paused:
        return true;
      case StudyPlanStatus.draft:
      case StudyPlanStatus.completed:
      case StudyPlanStatus.cancelled:
      case StudyPlanStatus.archived:
        return false;
    }
  }

  /// Whether the plan can be archived in this status
  bool get canBeArchived {
    switch (this) {
      case StudyPlanStatus.completed:
      case StudyPlanStatus.cancelled:
        return true;
      case StudyPlanStatus.draft:
      case StudyPlanStatus.active:
      case StudyPlanStatus.paused:
      case StudyPlanStatus.archived:
        return false;
    }
  }
}

/// Helper methods for StudyPlanStatus
class StudyPlanStatusHelper {
  /// Get all study plan statuses
  static List<StudyPlanStatus> get allStatuses => StudyPlanStatus.values;

  /// Get active statuses (draft, active, paused)
  static List<StudyPlanStatus> get activeStatuses => [
    StudyPlanStatus.draft,
    StudyPlanStatus.active,
    StudyPlanStatus.paused,
  ];

  /// Get completed statuses (completed, cancelled, archived)
  static List<StudyPlanStatus> get completedStatuses => [
    StudyPlanStatus.completed,
    StudyPlanStatus.cancelled,
    StudyPlanStatus.archived,
  ];

  /// Get editable statuses
  static List<StudyPlanStatus> get editableStatuses =>
      StudyPlanStatus.values.where((status) => status.canBeEdited).toList();

  /// Get study plan status from string
  static StudyPlanStatus? fromString(String value) {
    try {
      return StudyPlanStatus.values.firstWhere(
        (status) => status.name == value,
      );
    } catch (e) {
      return null;
    }
  }
}
