import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';

/// Enum representing how a study plan was created
enum PlanOrigin { scratch, template, duplicate, import }

extension PlanOriginExtension on PlanOrigin {
  /// Display name for the plan origin
  String get displayName {
    switch (this) {
      case PlanOrigin.scratch:
        return 'From Scratch';
      case PlanOrigin.template:
        return 'From Template';
      case PlanOrigin.duplicate:
        return 'Duplicated';
      case PlanOrigin.import:
        return 'Imported';
    }
  }

  /// Description for the plan origin
  String get description {
    switch (this) {
      case PlanOrigin.scratch:
        return 'Created from scratch';
      case PlanOrigin.template:
        return 'Created from template';
      case PlanOrigin.duplicate:
        return 'Duplicated from existing plan';
      case PlanOrigin.import:
        return 'Imported from external source';
    }
  }

  /// Icon for the plan origin
  IconData get icon {
    switch (this) {
      case PlanOrigin.scratch:
        return Symbols.create;
      case PlanOrigin.template:
        return Symbols.content_copy;
      case PlanOrigin.duplicate:
        return Symbols.file_copy;
      case PlanOrigin.import:
        return Symbols.file_download;
    }
  }

  /// Color associated with the plan origin
  Color get color {
    switch (this) {
      case PlanOrigin.scratch:
        return Colors.blue;
      case PlanOrigin.template:
        return Colors.green;
      case PlanOrigin.duplicate:
        return Colors.orange;
      case PlanOrigin.import:
        return Colors.purple;
    }
  }

  /// Whether this origin requires a source reference
  bool get requiresSourceReference {
    switch (this) {
      case PlanOrigin.template:
      case PlanOrigin.duplicate:
        return true;
      case PlanOrigin.scratch:
      case PlanOrigin.import:
        return false;
    }
  }

  /// Whether this origin allows customization during creation
  bool get allowsCustomization {
    switch (this) {
      case PlanOrigin.scratch:
      case PlanOrigin.template:
        return true;
      case PlanOrigin.duplicate:
      case PlanOrigin.import:
        return false; // Usually copied as-is, then edited
    }
  }
}

/// Helper methods for PlanOrigin
class PlanOriginHelper {
  /// Get all plan origins
  static List<PlanOrigin> get allOrigins => PlanOrigin.values;

  /// Get origins that require source references
  static List<PlanOrigin> get originsRequiringSource => PlanOrigin.values
      .where((origin) => origin.requiresSourceReference)
      .toList();

  /// Get origins that allow customization
  static List<PlanOrigin> get customizableOrigins =>
      PlanOrigin.values.where((origin) => origin.allowsCustomization).toList();

  /// Get plan origin from string
  static PlanOrigin? fromString(String value) {
    try {
      return PlanOrigin.values.firstWhere((origin) => origin.name == value);
    } catch (e) {
      return null;
    }
  }
}
