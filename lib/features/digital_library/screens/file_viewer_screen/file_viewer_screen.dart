// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:share_plus/share_plus.dart';
import '../../models/file_model.dart';
import '../../enums/viewer_type.dart';
import '../../enums/library_file_type.dart';
import '../../services/file_viewer_service.dart';

import '../../widgets/image_viewer_widget.dart';
import '../../widgets/pdf_viewer_widget.dart';
import '../../widgets/placeholder_viewer_widget.dart';

/// Full-screen file viewer with navigation support
class FileViewerScreen extends StatefulWidget {
  /// The file to view
  final FileModel fileModel;

  /// Optional list of files for navigation
  final List<FileModel>? fileList;

  /// Current index in the file list
  final int? currentIndex;

  const FileViewerScreen({
    super.key,
    required this.fileModel,
    this.fileList,
    this.currentIndex,
  });

  @override
  State<FileViewerScreen> createState() => _FileViewerScreenState();
}

class _FileViewerScreenState extends State<FileViewerScreen> {
  late FileModel _currentFile;
  late int _currentIndex;
  bool _isLoading = false;
  String? _errorMessage;

  final FileViewerService _viewerService = FileViewerService();

  @override
  void initState() {
    super.initState();
    _currentFile = widget.fileModel;
    _currentIndex = widget.currentIndex ?? 0;

    // Set system UI overlay style for full-screen viewing
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);

    _initializeFile();
  }

  @override
  void dispose() {
    // Restore system UI
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    super.dispose();
  }

  /// Initialize file for viewing
  void _initializeFile() async {
    if (!_currentFile.isAvailableForViewing) {
      setState(() {
        _isLoading = true;
      });

      try {
        final downloadedFile = await _viewerService.downloadForViewing(
          _currentFile,
        );
        if (downloadedFile != null) {
          setState(() {
            _currentFile = downloadedFile;
            _isLoading = false;
          });
        } else {
          setState(() {
            _isLoading = false;
            _errorMessage = 'Failed to download file for viewing';
          });
        }
      } catch (e) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Error preparing file: $e';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Main content
          _buildMainContent(),

          // Top app bar
          _buildTopAppBar(),

          // Navigation controls (if multiple files)
          if (widget.fileList != null && widget.fileList!.length > 1)
            _buildNavigationControls(),

          // Loading overlay
          if (_isLoading)
            Container(
              color: Colors.black54,
              child: const Center(
                child: CircularProgressIndicator(color: Colors.white),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildMainContent() {
    if (_errorMessage != null) {
      return _buildErrorView();
    }

    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.white),
      );
    }

    final viewerType = _viewerService.getViewerType(_currentFile);

    switch (viewerType) {
      case ViewerType.image:
        if (widget.fileList != null) {
          final imageFiles = widget.fileList!
              .where((file) => file.fileType == FileType.image)
              .toList();
          final imageIndex = imageFiles.indexOf(_currentFile);

          return GalleryImageViewerWidget(
            imageFiles: imageFiles,
            initialIndex: imageIndex >= 0 ? imageIndex : 0,
          );
        } else {
          return ImageViewerWidget(fileModel: _currentFile);
        }

      case ViewerType.pdf:
        return PDFViewerWidget(fileModel: _currentFile);

      case ViewerType.placeholder:
      case ViewerType.external:
        return PlaceholderViewerWidget(fileModel: _currentFile);
    }
  }

  Widget _buildTopAppBar() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: EdgeInsets.only(
          top: MediaQuery.of(context).padding.top,
          left: 16.w,
          right: 16.w,
          bottom: 16.h,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.black.withValues(alpha: 0.7), Colors.transparent],
          ),
        ),
        child: Row(
          children: [
            // Back button
            IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Symbols.arrow_back_ios),
              color: Colors.white,
              iconSize: 24.sp,
            ),

            SizedBox(width: 8.w),

            // File name
            Expanded(
              child: Text(
                _currentFile.fileName,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // Share button
            IconButton(
              onPressed: _shareFile,
              icon: const Icon(Symbols.share),
              color: Colors.white,
              iconSize: 24.sp,
            ),

            // More options button
            IconButton(
              onPressed: _showMoreOptions,
              icon: const Icon(Symbols.more_vert),
              color: Colors.white,
              iconSize: 24.sp,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavigationControls() {
    return Positioned(
      bottom: 20,
      left: 20,
      right: 20,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        decoration: BoxDecoration(
          color: Colors.black54,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Previous button
            IconButton(
              onPressed: _currentIndex > 0 ? _goToPrevious : null,
              icon: const Icon(Symbols.chevron_left),
              color: Colors.white,
              iconSize: 24.sp,
            ),

            // File counter
            Text(
              '${_currentIndex + 1} / ${widget.fileList!.length}',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
              ),
            ),

            // Next button
            IconButton(
              onPressed: _currentIndex < widget.fileList!.length - 1
                  ? _goToNext
                  : null,
              icon: const Icon(Symbols.chevron_right),
              color: Colors.white,
              iconSize: 24.sp,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Symbols.error, size: 64, color: Colors.white54),
          SizedBox(height: 16.h),
          Text(
            'Error Loading File',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            _errorMessage!,
            style: TextStyle(color: Colors.white70, fontSize: 14.sp),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _goToPrevious() {
    if (widget.fileList != null && _currentIndex > 0) {
      setState(() {
        _currentIndex--;
        _currentFile = widget.fileList![_currentIndex];
        _errorMessage = null;
      });
      _initializeFile();
    }
  }

  void _goToNext() {
    if (widget.fileList != null &&
        _currentIndex < widget.fileList!.length - 1) {
      setState(() {
        _currentIndex++;
        _currentFile = widget.fileList![_currentIndex];
        _errorMessage = null;
      });
      _initializeFile();
    }
  }

  void _shareFile() async {
    try {
      if (_currentFile.isLocal && _currentFile.localPath != null) {
        await Share.shareXFiles([XFile(_currentFile.localPath!)]);
      } else if (_currentFile.url != null) {
        await Share.share(_currentFile.url!);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to share file: $e')));
      }
    }
  }

  void _showMoreOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.grey[900],
      builder: (context) => Container(
        padding: EdgeInsets.all(16.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Symbols.open_in_new, color: Colors.white),
              title: const Text(
                'Open with External App',
                style: TextStyle(color: Colors.white),
              ),
              onTap: () {
                Navigator.pop(context);
                _viewerService.openFileExternally(_currentFile);
              },
            ),
            ListTile(
              leading: const Icon(Symbols.info, color: Colors.white),
              title: const Text(
                'File Info',
                style: TextStyle(color: Colors.white),
              ),
              onTap: () {
                Navigator.pop(context);
                _showFileInfo();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showFileInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('File Information'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Name: ${_currentFile.fileName}'),
            Text('Type: ${_currentFile.fileType.label}'),
            if (_currentFile.fileSizeBytes != null)
              Text('Size: ${_currentFile.fileSizeString}'),
            if (_currentFile.createdAt != null)
              Text('Created: ${_currentFile.createdAt}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
