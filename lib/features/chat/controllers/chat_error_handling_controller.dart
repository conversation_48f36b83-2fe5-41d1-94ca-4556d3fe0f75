import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import 'chat_controller.dart';
import 'message_controller.dart';
import 'chat_realtime_controller.dart';

/// Logger for chat error handling controller
final _logger = Logger();

/// Enum for different types of chat errors
enum ChatErrorType {
  network,
  permission,
  validation,
  notFound,
  serverError,
  timeout,
  unknown,
}

/// Extension for chat error types
extension ChatErrorTypeExtension on ChatErrorType {
  String get displayName {
    switch (this) {
      case ChatErrorType.network:
        return 'Network Error';
      case ChatErrorType.permission:
        return 'Permission Denied';
      case ChatErrorType.validation:
        return 'Validation Error';
      case ChatErrorType.notFound:
        return 'Not Found';
      case ChatErrorType.serverError:
        return 'Server Error';
      case ChatErrorType.timeout:
        return 'Request Timeout';
      case ChatErrorType.unknown:
        return 'Unknown Error';
    }
  }

  String get description {
    switch (this) {
      case ChatErrorType.network:
        return 'Please check your internet connection and try again.';
      case ChatErrorType.permission:
        return 'You don\'t have permission to perform this action.';
      case ChatErrorType.validation:
        return 'Please check your input and try again.';
      case ChatErrorType.notFound:
        return 'The requested resource was not found.';
      case ChatErrorType.serverError:
        return 'Something went wrong on our end. Please try again later.';
      case ChatErrorType.timeout:
        return 'The request took too long. Please try again.';
      case ChatErrorType.unknown:
        return 'An unexpected error occurred. Please try again.';
    }
  }
}

/// Model for chat errors
class ChatError {
  final ChatErrorType type;
  final String message;
  final String? details;
  final DateTime timestamp;
  final String? context;
  final bool isRetryable;

  const ChatError({
    required this.type,
    required this.message,
    this.details,
    required this.timestamp,
    this.context,
    this.isRetryable = true,
  });

  /// Create from exception
  factory ChatError.fromException(
    Exception exception, {
    String? context,
    ChatErrorType? type,
  }) {
    final message = exception.toString();
    final errorType = type ?? _determineErrorType(message);

    return ChatError(
      type: errorType,
      message: message,
      timestamp: DateTime.now(),
      context: context,
      isRetryable: _isRetryable(errorType),
    );
  }

  /// Determine error type from message
  static ChatErrorType _determineErrorType(String message) {
    final lowerMessage = message.toLowerCase();

    if (lowerMessage.contains('network') || lowerMessage.contains('connection')) {
      return ChatErrorType.network;
    } else if (lowerMessage.contains('permission') || lowerMessage.contains('unauthorized')) {
      return ChatErrorType.permission;
    } else if (lowerMessage.contains('validation') || lowerMessage.contains('invalid')) {
      return ChatErrorType.validation;
    } else if (lowerMessage.contains('not found') || lowerMessage.contains('404')) {
      return ChatErrorType.notFound;
    } else if (lowerMessage.contains('server') || lowerMessage.contains('500')) {
      return ChatErrorType.serverError;
    } else if (lowerMessage.contains('timeout')) {
      return ChatErrorType.timeout;
    } else {
      return ChatErrorType.unknown;
    }
  }

  /// Check if error type is retryable
  static bool _isRetryable(ChatErrorType type) {
    switch (type) {
      case ChatErrorType.network:
      case ChatErrorType.serverError:
      case ChatErrorType.timeout:
        return true;
      case ChatErrorType.permission:
      case ChatErrorType.validation:
      case ChatErrorType.notFound:
      case ChatErrorType.unknown:
        return false;
    }
  }

  /// Get user-friendly message
  String get userMessage => type.description;

  @override
  String toString() {
    return 'ChatError(type: $type, message: $message, context: $context)';
  }
}

/// State notifier for managing chat errors
class ChatErrorNotifier extends StateNotifier<List<ChatError>> {
  ChatErrorNotifier() : super([]);

  /// Add an error
  void addError(ChatError error) {
    state = [...state, error];
    _logger.e('Chat error added: ${error.type.displayName} - ${error.message}');
  }

  /// Add error from exception
  void addErrorFromException(Exception exception, {String? context}) {
    final error = ChatError.fromException(exception, context: context);
    addError(error);
  }

  /// Remove an error
  void removeError(ChatError error) {
    state = state.where((e) => e != error).toList();
    _logger.d('Chat error removed: ${error.type.displayName}');
  }

  /// Clear all errors
  void clearErrors() {
    state = [];
    _logger.d('All chat errors cleared');
  }

  /// Clear errors by type
  void clearErrorsByType(ChatErrorType type) {
    state = state.where((error) => error.type != type).toList();
    _logger.d('Cleared errors of type: ${type.displayName}');
  }

  /// Clear errors by context
  void clearErrorsByContext(String context) {
    state = state.where((error) => error.context != context).toList();
    _logger.d('Cleared errors for context: $context');
  }

  /// Get errors by type
  List<ChatError> getErrorsByType(ChatErrorType type) {
    return state.where((error) => error.type == type).toList();
  }

  /// Get retryable errors
  List<ChatError> getRetryableErrors() {
    return state.where((error) => error.isRetryable).toList();
  }

  /// Check if has errors of type
  bool hasErrorsOfType(ChatErrorType type) {
    return state.any((error) => error.type == type);
  }
}

/// Provider for chat errors
final chatErrorProvider = StateNotifierProvider<ChatErrorNotifier, List<ChatError>>((ref) {
  return ChatErrorNotifier();
});

/// State notifier for retry mechanisms
class ChatRetryNotifier extends StateNotifier<Map<String, int>> {
  ChatRetryNotifier() : super({});

  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(seconds: 2);

  /// Attempt to retry an operation
  Future<T> retryOperation<T>(
    String operationId,
    Future<T> Function() operation, {
    int? maxAttempts,
    Duration? delay,
  }) async {
    final attempts = maxAttempts ?? maxRetries;
    final retryDelay = delay ?? ChatRetryNotifier.retryDelay;

    for (int attempt = 1; attempt <= attempts; attempt++) {
      try {
        state = {...state, operationId: attempt};
        _logger.i('Attempting operation $operationId (attempt $attempt/$attempts)');

        final result = await operation();
        
        // Success - remove from retry state
        final newState = Map<String, int>.from(state);
        newState.remove(operationId);
        state = newState;
        
        _logger.i('Operation $operationId succeeded on attempt $attempt');
        return result;
      } catch (e) {
        _logger.w('Operation $operationId failed on attempt $attempt: $e');

        if (attempt == attempts) {
          // Final attempt failed - remove from retry state
          final newState = Map<String, int>.from(state);
          newState.remove(operationId);
          state = newState;
          
          _logger.e('Operation $operationId failed after $attempts attempts');
          rethrow;
        }

        // Wait before next attempt
        await Future.delayed(retryDelay * attempt);
      }
    }

    throw Exception('Retry operation failed unexpectedly');
  }

  /// Get current retry attempt for operation
  int getRetryAttempt(String operationId) {
    return state[operationId] ?? 0;
  }

  /// Check if operation is being retried
  bool isRetrying(String operationId) {
    return state.containsKey(operationId);
  }

  /// Cancel retry for operation
  void cancelRetry(String operationId) {
    final newState = Map<String, int>.from(state);
    newState.remove(operationId);
    state = newState;
    _logger.i('Cancelled retry for operation: $operationId');
  }
}

/// Provider for retry mechanisms
final chatRetryProvider = StateNotifierProvider<ChatRetryNotifier, Map<String, int>>((ref) {
  return ChatRetryNotifier();
});

/// State notifier for loading states
class ChatLoadingNotifier extends StateNotifier<Map<String, bool>> {
  ChatLoadingNotifier() : super({});

  /// Set loading state for operation
  void setLoading(String operationId, bool isLoading) {
    if (isLoading) {
      state = {...state, operationId: true};
    } else {
      final newState = Map<String, bool>.from(state);
      newState.remove(operationId);
      state = newState;
    }
    _logger.d('Set loading state for $operationId: $isLoading');
  }

  /// Check if operation is loading
  bool isLoading(String operationId) {
    return state[operationId] ?? false;
  }

  /// Check if any operation is loading
  bool get hasAnyLoading => state.isNotEmpty;

  /// Get all loading operations
  List<String> get loadingOperations => state.keys.toList();

  /// Clear all loading states
  void clearAll() {
    state = {};
    _logger.d('Cleared all loading states');
  }
}

/// Provider for loading states
final chatLoadingProvider = StateNotifierProvider<ChatLoadingNotifier, Map<String, bool>>((ref) {
  return ChatLoadingNotifier();
});

/// Provider for error recovery utilities
final errorRecoveryProvider = Provider<ErrorRecoveryUtils>((ref) {
  return ErrorRecoveryUtils(ref);
});

/// Utility class for error recovery operations
class ErrorRecoveryUtils {
  ErrorRecoveryUtils(this._ref);

  final Ref _ref;

  /// Recover from chat loading error
  Future<void> recoverChatLoading() async {
    try {
      _logger.i('Attempting to recover from chat loading error');
      
      // Refresh chat providers
      _ref.invalidate(userChatsProvider);
      _ref.invalidate(chatRealtimeProvider);
      
      // Clear related errors
      final errorNotifier = _ref.read(chatErrorProvider.notifier);
      errorNotifier.clearErrorsByContext('chat_loading');
      
      _logger.i('Chat loading recovery completed');
    } catch (e) {
      _logger.e('Error during chat loading recovery: $e');
      rethrow;
    }
  }

  /// Recover from message sending error
  Future<void> recoverMessageSending(String chatId) async {
    try {
      _logger.i('Attempting to recover from message sending error for chat: $chatId');
      
      // Refresh message providers
      _ref.invalidate(chatMessagesProvider(chatId));
      _ref.invalidate(messagesRealtimeProvider(chatId));
      
      // Clear related errors
      final errorNotifier = _ref.read(chatErrorProvider.notifier);
      errorNotifier.clearErrorsByContext('message_sending_$chatId');
      
      _logger.i('Message sending recovery completed for chat: $chatId');
    } catch (e) {
      _logger.e('Error during message sending recovery: $e');
      rethrow;
    }
  }

  /// Recover from connection error
  Future<void> recoverConnection() async {
    try {
      _logger.i('Attempting to recover from connection error');
      
      // Reset connection status
      final connectionStatus = _ref.read(connectionStatusProvider.notifier);
      connectionStatus.setConnectionStatus(ConnectionStatus.connecting);
      
      // Refresh real-time providers
      final realtimeSync = _ref.read(realtimeSyncManagerProvider);
      realtimeSync.refreshAllStreams();
      
      // Clear network errors
      final errorNotifier = _ref.read(chatErrorProvider.notifier);
      errorNotifier.clearErrorsByType(ChatErrorType.network);
      
      // Simulate connection recovery
      await Future.delayed(const Duration(seconds: 2));
      connectionStatus.setConnectionStatus(ConnectionStatus.connected);
      
      _logger.i('Connection recovery completed');
    } catch (e) {
      _logger.e('Error during connection recovery: $e');
      rethrow;
    }
  }

  /// Generic retry with error handling
  Future<T> retryWithErrorHandling<T>(
    String operationId,
    Future<T> Function() operation, {
    String? context,
    int? maxAttempts,
  }) async {
    final retryNotifier = _ref.read(chatRetryProvider.notifier);
    final errorNotifier = _ref.read(chatErrorProvider.notifier);
    final loadingNotifier = _ref.read(chatLoadingProvider.notifier);

    try {
      loadingNotifier.setLoading(operationId, true);
      
      final result = await retryNotifier.retryOperation(
        operationId,
        operation,
        maxAttempts: maxAttempts,
      );
      
      // Clear any previous errors for this operation
      if (context != null) {
        errorNotifier.clearErrorsByContext(context);
      }
      
      return result;
    } catch (e) {
      // Add error to state
      final error = ChatError.fromException(
        e is Exception ? e : Exception(e.toString()),
        context: context,
      );
      errorNotifier.addError(error);
      
      rethrow;
    } finally {
      loadingNotifier.setLoading(operationId, false);
    }
  }
}
