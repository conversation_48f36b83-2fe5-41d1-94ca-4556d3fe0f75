import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import 'chat_controller.dart';
import 'participant_controller.dart';

/// Logger for presence controller
final _logger = Logger();

/// Enum for user presence status
enum PresenceStatus { online, away, offline, busy }

/// Extension for presence status
extension PresenceStatusExtension on PresenceStatus {
  String get value {
    switch (this) {
      case PresenceStatus.online:
        return 'online';
      case PresenceStatus.away:
        return 'away';
      case PresenceStatus.offline:
        return 'offline';
      case PresenceStatus.busy:
        return 'busy';
    }
  }

  String get displayName {
    switch (this) {
      case PresenceStatus.online:
        return 'Online';
      case PresenceStatus.away:
        return 'Away';
      case PresenceStatus.offline:
        return 'Offline';
      case PresenceStatus.busy:
        return 'Busy';
    }
  }

  static PresenceStatus fromString(String value) {
    switch (value.toLowerCase()) {
      case 'online':
        return PresenceStatus.online;
      case 'away':
        return PresenceStatus.away;
      case 'offline':
        return PresenceStatus.offline;
      case 'busy':
        return PresenceStatus.busy;
      default:
        return PresenceStatus.offline;
    }
  }
}

/// Model for user presence information
class UserPresence {
  final String userId;
  final PresenceStatus status;
  final DateTime lastSeen;
  final String? customMessage;

  const UserPresence({
    required this.userId,
    required this.status,
    required this.lastSeen,
    this.customMessage,
  });

  /// Create from JSON
  factory UserPresence.fromJson(Map<String, dynamic> json) {
    return UserPresence(
      userId: json['userId'] as String,
      status: PresenceStatusExtension.fromString(
        json['status'] as String? ?? 'offline',
      ),
      lastSeen: DateTime.parse(json['lastSeen'] as String),
      customMessage: json['customMessage'] as String?,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'status': status.value,
      'lastSeen': lastSeen.toIso8601String(),
      'customMessage': customMessage,
    };
  }

  /// Copy with new values
  UserPresence copyWith({
    String? userId,
    PresenceStatus? status,
    DateTime? lastSeen,
    String? customMessage,
  }) {
    return UserPresence(
      userId: userId ?? this.userId,
      status: status ?? this.status,
      lastSeen: lastSeen ?? this.lastSeen,
      customMessage: customMessage ?? this.customMessage,
    );
  }

  /// Check if user is currently online
  bool get isOnline => status == PresenceStatus.online;

  /// Check if user was recently active (within last 5 minutes)
  bool get isRecentlyActive {
    final now = DateTime.now();
    return now.difference(lastSeen).inMinutes < 5;
  }

  /// Get display text for last seen
  String get lastSeenText {
    final now = DateTime.now();
    final difference = now.difference(lastSeen);

    if (isOnline) {
      return 'Online';
    } else if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}

/// State notifier for managing current user's presence
class CurrentUserPresenceNotifier extends StateNotifier<UserPresence> {
  CurrentUserPresenceNotifier(Ref ref) : super(_getInitialPresence(ref)) {
    _startPresenceUpdates();
  }

  Timer? _presenceTimer;

  /// Get initial presence state
  static UserPresence _getInitialPresence(Ref ref) {
    final userId = ref.read(currentUserIdProvider);
    return UserPresence(
      userId: userId,
      status: PresenceStatus.online,
      lastSeen: DateTime.now(),
    );
  }

  /// Start automatic presence updates
  void _startPresenceUpdates() {
    _presenceTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      _updatePresence();
    });
    _logger.d('Started presence updates for user: ${state.userId}');
  }

  /// Update presence automatically
  void _updatePresence() {
    state = state.copyWith(lastSeen: DateTime.now());
    _logger.d('Updated presence for user: ${state.userId}');
  }

  /// Set presence status
  void setStatus(PresenceStatus status) {
    state = state.copyWith(status: status, lastSeen: DateTime.now());
    _logger.i(
      'Set presence status to ${status.value} for user: ${state.userId}',
    );
  }

  /// Set custom status message
  void setCustomMessage(String? message) {
    state = state.copyWith(customMessage: message, lastSeen: DateTime.now());
    _logger.i('Set custom message for user: ${state.userId}');
  }

  /// Set user as online
  void setOnline() => setStatus(PresenceStatus.online);

  /// Set user as away
  void setAway() => setStatus(PresenceStatus.away);

  /// Set user as busy
  void setBusy() => setStatus(PresenceStatus.busy);

  /// Set user as offline
  void setOffline() => setStatus(PresenceStatus.offline);

  @override
  void dispose() {
    _presenceTimer?.cancel();
    super.dispose();
  }
}

/// Provider for current user's presence
final currentUserPresenceProvider =
    StateNotifierProvider<CurrentUserPresenceNotifier, UserPresence>((ref) {
      return CurrentUserPresenceNotifier(ref);
    });

/// State notifier for managing other users' presence
class UsersPresenceNotifier extends StateNotifier<Map<String, UserPresence>> {
  UsersPresenceNotifier() : super({});

  /// Update presence for a user
  void updateUserPresence(UserPresence presence) {
    state = {...state, presence.userId: presence};
    _logger.d(
      'Updated presence for user: ${presence.userId} to ${presence.status.value}',
    );
  }

  /// Update multiple users' presence
  void updateMultiplePresence(List<UserPresence> presenceList) {
    final newState = Map<String, UserPresence>.from(state);
    for (final presence in presenceList) {
      newState[presence.userId] = presence;
    }
    state = newState;
    _logger.d('Updated presence for ${presenceList.length} users');
  }

  /// Remove user presence
  void removeUserPresence(String userId) {
    final newState = Map<String, UserPresence>.from(state);
    newState.remove(userId);
    state = newState;
    _logger.d('Removed presence for user: $userId');
  }

  /// Get presence for a specific user
  UserPresence? getUserPresence(String userId) {
    return state[userId];
  }

  /// Get online users
  List<UserPresence> getOnlineUsers() {
    return state.values.where((presence) => presence.isOnline).toList();
  }

  /// Get recently active users
  List<UserPresence> getRecentlyActiveUsers() {
    return state.values.where((presence) => presence.isRecentlyActive).toList();
  }
}

/// Provider for other users' presence
final usersPresenceProvider =
    StateNotifierProvider<UsersPresenceNotifier, Map<String, UserPresence>>((
      ref,
    ) {
      return UsersPresenceNotifier();
    });

/// Provider to get presence for a specific user
final userPresenceProvider = Provider.family<UserPresence?, String>((
  ref,
  userId,
) {
  final currentUserId = ref.read(currentUserIdProvider);

  if (userId == currentUserId) {
    return ref.watch(currentUserPresenceProvider);
  }

  final usersPresence = ref.watch(usersPresenceProvider);
  return usersPresence[userId];
});

/// Provider to get presence for chat participants
final chatParticipantsPresenceProvider =
    FutureProvider.family<Map<String, UserPresence>, String>((
      ref,
      chatId,
    ) async {
      final participants = await ref.watch(
        chatParticipantsProvider(chatId).future,
      );
      final usersPresence = ref.watch(usersPresenceProvider);
      final currentUserPresence = ref.watch(currentUserPresenceProvider);

      final participantsPresence = <String, UserPresence>{};

      for (final participant in participants) {
        final userId = participant.userId;
        final currentUserId = ref.read(currentUserIdProvider);

        if (userId == currentUserId) {
          participantsPresence[userId] = currentUserPresence;
        } else {
          final presence = usersPresence[userId];
          if (presence != null) {
            participantsPresence[userId] = presence;
          }
        }
      }

      _logger.d(
        'Got presence for ${participantsPresence.length} participants in chat $chatId',
      );
      return participantsPresence;
    });

/// Provider to get online participants count for a chat
final chatOnlineParticipantsCountProvider = FutureProvider.family<int, String>((
  ref,
  chatId,
) async {
  final participantsPresence = await ref.watch(
    chatParticipantsPresenceProvider(chatId).future,
  );

  final onlineCount = participantsPresence.values
      .where((presence) => presence.isOnline)
      .length;

  _logger.d('Online participants in chat $chatId: $onlineCount');
  return onlineCount;
});

/// State notifier for managing presence simulation (for testing)
class PresenceSimulatorNotifier extends StateNotifier<bool> {
  PresenceSimulatorNotifier(this._ref) : super(false);

  final Ref _ref;
  Timer? _simulationTimer;

  /// Start simulating presence updates
  void startSimulation() {
    if (state) return;

    state = true;
    _simulationTimer = Timer.periodic(const Duration(seconds: 10), (_) {
      _simulatePresenceUpdates();
    });
    _logger.i('Started presence simulation');
  }

  /// Stop simulating presence updates
  void stopSimulation() {
    if (!state) return;

    state = false;
    _simulationTimer?.cancel();
    _simulationTimer = null;
    _logger.i('Stopped presence simulation');
  }

  /// Simulate presence updates for testing
  void _simulatePresenceUpdates() {
    final usersPresence = _ref.read(usersPresenceProvider.notifier);

    // Simulate some users going online/offline
    final testUsers = ['user1', 'user2', 'user3', 'user4', 'user5'];
    final statuses = PresenceStatus.values;

    for (final userId in testUsers) {
      final randomStatus =
          statuses[DateTime.now().millisecondsSinceEpoch % statuses.length];
      final presence = UserPresence(
        userId: userId,
        status: randomStatus,
        lastSeen: DateTime.now(),
      );
      usersPresence.updateUserPresence(presence);
    }

    _logger.d('Simulated presence updates for ${testUsers.length} users');
  }

  @override
  void dispose() {
    stopSimulation();
    super.dispose();
  }
}

/// Provider for presence simulation
final presenceSimulatorProvider =
    StateNotifierProvider<PresenceSimulatorNotifier, bool>((ref) {
      return PresenceSimulatorNotifier(ref);
    });

/// Provider for presence manager
final presenceManagerProvider = Provider<PresenceManager>((ref) {
  return PresenceManager(ref);
});

/// Class for managing overall presence functionality
class PresenceManager {
  PresenceManager(this._ref);

  final Ref _ref;

  /// Initialize presence system
  void initialize() {
    _logger.i('Initializing presence system');

    // Set current user as online
    final currentUserPresence = _ref.read(currentUserPresenceProvider.notifier);
    currentUserPresence.setOnline();

    // Start simulation for testing (remove in production)
    final simulator = _ref.read(presenceSimulatorProvider.notifier);
    simulator.startSimulation();
  }

  /// Handle app lifecycle changes
  void handleAppLifecycleChange(String lifecycle) {
    final currentUserPresence = _ref.read(currentUserPresenceProvider.notifier);

    switch (lifecycle) {
      case 'resumed':
        currentUserPresence.setOnline();
        _logger.i('App resumed, set user online');
        break;
      case 'paused':
        currentUserPresence.setAway();
        _logger.i('App paused, set user away');
        break;
      case 'detached':
        currentUserPresence.setOffline();
        _logger.i('App detached, set user offline');
        break;
    }
  }

  /// Update presence based on user activity
  void updateActivityPresence() {
    final currentUserPresence = _ref.read(currentUserPresenceProvider.notifier);
    currentUserPresence.setOnline();
    _logger.d('Updated presence based on user activity');
  }

  /// Cleanup presence system
  void dispose() {
    _logger.i('Disposing presence system');

    // Set user as offline
    final currentUserPresence = _ref.read(currentUserPresenceProvider.notifier);
    currentUserPresence.setOffline();

    // Stop simulation
    final simulator = _ref.read(presenceSimulatorProvider.notifier);
    simulator.stopSimulation();
  }
}
