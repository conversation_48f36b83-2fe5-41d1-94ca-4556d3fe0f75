import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../models/chat_models.dart';
import '../enums/chat_enums.dart';
import 'chat_repository_providers.dart';
import 'chat_controller.dart';

/// Logger for participant controller
final _logger = Logger();

/// Provider to fetch participants for a specific chat
final chatParticipantsProvider = FutureProvider.family<List<ChatParticipant>, String>((ref, chatId) async {
  final repository = ref.read(participantRepositoryProvider);

  try {
    _logger.i('Fetching participants for chat: $chatId');
    final participants = await repository.getParticipantsForChat(chatId);
    _logger.i('Successfully fetched ${participants.length} participants for chat $chatId');
    return participants;
  } catch (e) {
    _logger.e('Error fetching participants for chat: $e');
    rethrow;
  }
});

/// Provider to get a specific participant in a chat
final participantProvider = FutureProvider.family<ChatParticipant?, Map<String, String>>((ref, params) async {
  final repository = ref.read(participantRepositoryProvider);
  final chatId = params['chatId']!;
  final userId = params['userId']!;

  try {
    _logger.i('Fetching participant $userId for chat: $chatId');
    final participant = await repository.getParticipant(chatId, userId);
    if (participant != null) {
      _logger.i('Successfully fetched participant: $userId');
    } else {
      _logger.w('Participant not found: $userId in chat $chatId');
    }
    return participant;
  } catch (e) {
    _logger.e('Error fetching participant: $e');
    rethrow;
  }
});

/// Provider to check if current user is a participant in a chat
final isParticipantProvider = FutureProvider.family<bool, String>((ref, chatId) async {
  final repository = ref.read(participantRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);

  try {
    _logger.i('Checking if user $userId is participant in chat: $chatId');
    final isParticipant = await repository.isParticipant(chatId, userId);
    _logger.i('User $userId is participant in chat $chatId: $isParticipant');
    return isParticipant;
  } catch (e) {
    _logger.e('Error checking participant status: $e');
    rethrow;
  }
});

/// Provider to get all chats where current user is a participant
final userParticipantChatsProvider = FutureProvider<List<String>>((ref) async {
  final repository = ref.read(participantRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);

  try {
    _logger.i('Fetching chats for user: $userId');
    final chatIds = await repository.getChatsForUser(userId);
    _logger.i('Successfully fetched ${chatIds.length} chats for user $userId');
    return chatIds;
  } catch (e) {
    _logger.e('Error fetching chats for user: $e');
    rethrow;
  }
});

/// State notifier for participant mutations (add, remove, update role, etc.)
class ParticipantMutationNotifier extends StateNotifier<AsyncValue<void>> {
  ParticipantMutationNotifier(this._ref) : super(const AsyncValue.data(null));

  final Ref _ref;

  /// Add a participant to a chat
  Future<void> addParticipant(String chatId, ChatParticipant participant) async {
    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      final repository = _ref.read(participantRepositoryProvider);

      _logger.i('Adding participant ${participant.userId} to chat: $chatId');
      await repository.addParticipantToChat(chatId, participant);

      // Invalidate related providers
      _ref.invalidate(chatParticipantsProvider(chatId));
      _ref.invalidate(chatByIdProvider(chatId));
      _ref.invalidate(userChatsProvider);
      _ref.invalidate(userParticipantChatsProvider);

      _logger.i('Successfully added participant ${participant.userId} to chat $chatId');
    });
  }

  /// Remove a participant from a chat
  Future<void> removeParticipant(String chatId, String userId) async {
    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      final repository = _ref.read(participantRepositoryProvider);

      _logger.i('Removing participant $userId from chat: $chatId');
      await repository.removeParticipantFromChat(chatId, userId);

      // Invalidate related providers
      _ref.invalidate(chatParticipantsProvider(chatId));
      _ref.invalidate(chatByIdProvider(chatId));
      _ref.invalidate(userChatsProvider);
      _ref.invalidate(userParticipantChatsProvider);

      _logger.i('Successfully removed participant $userId from chat $chatId');
    });
  }

  /// Update participant role
  Future<void> updateParticipantRole(String chatId, String userId, ChatRole newRole) async {
    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      final repository = _ref.read(participantRepositoryProvider);

      _logger.i('Updating participant $userId role to ${newRole.value} in chat: $chatId');
      await repository.updateParticipantRole(chatId, userId, newRole);

      // Invalidate related providers
      _ref.invalidate(chatParticipantsProvider(chatId));
      _ref.invalidate(chatByIdProvider(chatId));
      _ref.invalidate(participantProvider({'chatId': chatId, 'userId': userId}));

      _logger.i('Successfully updated participant $userId role in chat $chatId');
    });
  }

  /// Update participant permissions
  Future<void> updateParticipantPermissions(
    String chatId,
    String userId,
    Map<String, bool> permissions,
  ) async {
    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      final repository = _ref.read(participantRepositoryProvider);

      _logger.i('Updating participant $userId permissions in chat: $chatId');
      await repository.updateParticipantPermissions(chatId, userId, permissions);

      // Invalidate related providers
      _ref.invalidate(chatParticipantsProvider(chatId));
      _ref.invalidate(chatByIdProvider(chatId));
      _ref.invalidate(participantProvider({'chatId': chatId, 'userId': userId}));

      _logger.i('Successfully updated participant $userId permissions in chat $chatId');
    });
  }

  /// Update participant's last read timestamp
  Future<void> updateLastReadAt(String chatId, String userId, DateTime timestamp) async {
    try {
      final repository = _ref.read(participantRepositoryProvider);

      _logger.i('Updating last read timestamp for participant $userId in chat: $chatId');
      await repository.updateLastReadAt(chatId, userId, timestamp);

      // Invalidate related providers
      _ref.invalidate(chatParticipantsProvider(chatId));
      _ref.invalidate(chatByIdProvider(chatId));
      _ref.invalidate(participantProvider({'chatId': chatId, 'userId': userId}));

      _logger.i('Successfully updated last read timestamp for participant $userId in chat $chatId');
    } catch (e) {
      _logger.e('Error updating last read timestamp: $e');
      rethrow;
    }
  }

  /// Mute/unmute chat for a participant
  Future<void> updateMuteStatus(String chatId, String userId, bool isMuted) async {
    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      final repository = _ref.read(participantRepositoryProvider);

      _logger.i('Updating mute status for participant $userId in chat: $chatId to $isMuted');
      await repository.updateMuteStatus(chatId, userId, isMuted);

      // Invalidate related providers
      _ref.invalidate(chatParticipantsProvider(chatId));
      _ref.invalidate(chatByIdProvider(chatId));
      _ref.invalidate(participantProvider({'chatId': chatId, 'userId': userId}));

      _logger.i('Successfully updated mute status for participant $userId in chat $chatId');
    });
  }
}

/// Provider for participant mutations
final participantMutationProvider = StateNotifierProvider<ParticipantMutationNotifier, AsyncValue<void>>((ref) {
  return ParticipantMutationNotifier(ref);
});

/// Provider to get participant statistics for a chat
final participantStatsProvider = FutureProvider.family<Map<String, dynamic>, String>((ref, chatId) async {
  final participants = await ref.watch(chatParticipantsProvider(chatId).future);

  final stats = {
    'total': participants.length,
    'admins': 0,
    'moderators': 0,
    'participants': 0,
    'observers': 0,
    'online': 0,
    'muted': 0,
  };

  for (final participant in participants) {
    // Count by role
    switch (participant.role) {
      case ChatRole.admin:
        stats['admins'] = (stats['admins'] as int) + 1;
        break;
      case ChatRole.moderator:
        stats['moderators'] = (stats['moderators'] as int) + 1;
        break;
      case ChatRole.participant:
        stats['participants'] = (stats['participants'] as int) + 1;
        break;
      case ChatRole.observer:
        stats['observers'] = (stats['observers'] as int) + 1;
        break;
    }

    // Count muted participants
    if (participant.isMuted) {
      stats['muted'] = (stats['muted'] as int) + 1;
    }

    // Count online participants (based on recent activity)
    final now = DateTime.now();
    final lastReadAt = participant.lastReadAt;
    if (lastReadAt != null && now.difference(lastReadAt).inMinutes < 5) {
      stats['online'] = (stats['online'] as int) + 1;
    }
  }

  _logger.i('Participant stats for chat $chatId: $stats');
  return stats;
});

/// Provider to check if current user has specific permissions in a chat
final currentUserPermissionsProvider = FutureProvider.family<Map<String, bool>, String>((ref, chatId) async {
  final userId = ref.read(currentUserIdProvider);
  final participant = await ref.watch(participantProvider({'chatId': chatId, 'userId': userId}).future);

  if (participant == null) {
    return {
      'canSendMessages': false,
      'canAddParticipants': false,
      'canRemoveParticipants': false,
      'canEditSettings': false,
      'canDeleteChat': false,
      'canMuteChat': false,
      'canLeaveChat': false,
    };
  }

  final isAdmin = participant.role == ChatRole.admin;
  final isModerator = participant.role == ChatRole.moderator;

  return {
    'canSendMessages': participant.canSendMessages,
    'canAddParticipants': participant.canAddParticipants || isAdmin || isModerator,
    'canRemoveParticipants': isAdmin || isModerator,
    'canEditSettings': isAdmin,
    'canDeleteChat': isAdmin,
    'canMuteChat': true, // Users can always mute/unmute for themselves
    'canLeaveChat': true, // Users can always leave (except maybe admins in some cases)
  };
});

/// State provider for participant search query
final participantSearchQueryProvider = StateProvider.family<String, String>((ref, chatId) => '');

/// Provider for filtered participants based on search
final filteredParticipantsProvider = FutureProvider.family<List<ChatParticipant>, String>((ref, chatId) async {
  final participants = await ref.watch(chatParticipantsProvider(chatId).future);
  final searchQuery = ref.watch(participantSearchQueryProvider(chatId));

  if (searchQuery.isEmpty) {
    return participants;
  }

  final filteredParticipants = participants.where((participant) {
    // Note: In a real app, you'd want to fetch user profiles to search by name
    // For now, we'll just search by user ID
    return participant.userId.toLowerCase().contains(searchQuery.toLowerCase());
  }).toList();

  _logger.i('Filtered participants for chat $chatId: ${filteredParticipants.length} results');
  return filteredParticipants;
});
