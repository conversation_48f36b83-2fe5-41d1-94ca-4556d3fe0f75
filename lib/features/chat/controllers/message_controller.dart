import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../models/chat_models.dart';
import '../enums/chat_enums.dart';
import 'chat_repository_providers.dart';
import 'chat_controller.dart';

/// Logger for message controller
final _logger = Logger();

/// Provider to fetch messages for a specific chat
final chatMessagesProvider = FutureProvider.family<List<MessageModel>, String>((
  ref,
  chatId,
) async {
  final repository = ref.read(messageRepositoryProvider);

  try {
    _logger.i('Fetching messages for chat: $chatId');
    final messages = await repository.getMessagesForChat(chatId);
    _logger.i(
      'Successfully fetched ${messages.length} messages for chat $chatId',
    );
    return messages;
  } catch (e) {
    _logger.e('Error fetching messages for chat: $e');
    rethrow;
  }
});

/// Provider to fetch a specific message by ID
final messageByIdProvider = FutureProvider.family<MessageModel?, String>((
  ref,
  messageId,
) async {
  final repository = ref.read(messageRepositoryProvider);

  try {
    _logger.i('Fetching message by ID: $messageId');
    final message = await repository.getMessageById(messageId);
    if (message != null) {
      _logger.i('Successfully fetched message: $messageId');
    } else {
      _logger.w('Message not found: $messageId');
    }
    return message;
  } catch (e) {
    _logger.e('Error fetching message by ID: $e');
    rethrow;
  }
});

/// Provider to fetch reactions for a specific message
final messageReactionsProvider =
    FutureProvider.family<List<MessageReaction>, String>((
      ref,
      messageId,
    ) async {
      final repository = ref.read(messageRepositoryProvider);

      try {
        _logger.i('Fetching reactions for message: $messageId');
        final reactions = await repository.getReactionsForMessage(messageId);
        _logger.i(
          'Successfully fetched ${reactions.length} reactions for message $messageId',
        );
        return reactions;
      } catch (e) {
        _logger.e('Error fetching message reactions: $e');
        rethrow;
      }
    });

/// Provider to search messages in a chat
final searchMessagesProvider =
    FutureProvider.family<List<MessageModel>, Map<String, String>>((
      ref,
      params,
    ) async {
      final repository = ref.read(messageRepositoryProvider);
      final chatId = params['chatId']!;
      final searchQuery = params['searchQuery']!;

      try {
        _logger.i('Searching messages in chat: $chatId for "$searchQuery"');
        final messages = await repository.searchMessagesInChat(
          chatId,
          searchQuery,
        );
        _logger.i(
          'Successfully found ${messages.length} messages matching "$searchQuery"',
        );
        return messages;
      } catch (e) {
        _logger.e('Error searching messages: $e');
        rethrow;
      }
    });

/// State notifier for message mutations (send, edit, delete, react)
class MessageMutationNotifier extends StateNotifier<AsyncValue<void>> {
  MessageMutationNotifier(this._ref) : super(const AsyncValue.data(null));

  final Ref _ref;

  /// Send a new message
  Future<MessageModel> sendMessage(MessageModel message) async {
    state = const AsyncValue.loading();

    return await AsyncValue.guard(() async {
      final repository = _ref.read(messageRepositoryProvider);
      final chatMutation = _ref.read(chatMutationProvider.notifier);

      _logger.i('Sending message to chat: ${message.chatId}');
      final sentMessage = await repository.sendMessage(message);

      // Update chat's last message
      await chatMutation.updateLastMessage(
        message.chatId,
        sentMessage.id,
        sentMessage.sentAt,
      );

      // Invalidate related providers
      _ref.invalidate(chatMessagesProvider(message.chatId));
      _ref.invalidate(userChatsProvider);

      _logger.i('Successfully sent message: ${sentMessage.id}');
      return sentMessage;
    }).then((result) {
      state = result.when(
        data: (_) => const AsyncValue.data(null),
        loading: () => const AsyncValue.loading(),
        error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
      );

      return result.when(
        data: (message) => message,
        loading: () => throw Exception('Unexpected loading state'),
        error: (error, stackTrace) => throw error,
      );
    });
  }

  /// Edit a message
  Future<MessageModel> editMessage(String messageId, String newContent) async {
    state = const AsyncValue.loading();

    return await AsyncValue.guard(() async {
      final repository = _ref.read(messageRepositoryProvider);

      _logger.i('Editing message: $messageId');
      final updatedMessage = await repository.updateMessage(
        messageId,
        newContent,
      );

      // Invalidate related providers
      _ref.invalidate(chatMessagesProvider(updatedMessage.chatId));
      _ref.invalidate(messageByIdProvider(messageId));

      _logger.i('Successfully edited message: $messageId');
      return updatedMessage;
    }).then((result) {
      state = result.when(
        data: (_) => const AsyncValue.data(null),
        loading: () => const AsyncValue.loading(),
        error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
      );

      return result.when(
        data: (message) => message,
        loading: () => throw Exception('Unexpected loading state'),
        error: (error, stackTrace) => throw error,
      );
    });
  }

  /// Delete a message
  Future<void> deleteMessage(String messageId, String chatId) async {
    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      final repository = _ref.read(messageRepositoryProvider);

      _logger.i('Deleting message: $messageId');
      await repository.deleteMessage(messageId);

      // Invalidate related providers
      _ref.invalidate(chatMessagesProvider(chatId));
      _ref.invalidate(messageByIdProvider(messageId));

      _logger.i('Successfully deleted message: $messageId');
    });
  }

  /// Add reaction to a message
  Future<void> addReaction(
    String messageId,
    String chatId,
    String emoji,
  ) async {
    try {
      final repository = _ref.read(messageRepositoryProvider);
      final userId = _ref.read(currentUserIdProvider);

      _logger.i('Adding reaction to message: $messageId');
      await repository.addReaction(messageId, userId, emoji);

      // Invalidate related providers
      _ref.invalidate(messageReactionsProvider(messageId));
      _ref.invalidate(chatMessagesProvider(chatId));

      _logger.i('Successfully added reaction to message: $messageId');
    } catch (e) {
      _logger.e('Error adding reaction: $e');
      rethrow;
    }
  }

  /// Remove reaction from a message
  Future<void> removeReaction(
    String messageId,
    String chatId,
    String emoji,
  ) async {
    try {
      final repository = _ref.read(messageRepositoryProvider);
      final userId = _ref.read(currentUserIdProvider);

      _logger.i('Removing reaction from message: $messageId');
      await repository.removeReaction(messageId, userId, emoji);

      // Invalidate related providers
      _ref.invalidate(messageReactionsProvider(messageId));
      _ref.invalidate(chatMessagesProvider(chatId));

      _logger.i('Successfully removed reaction from message: $messageId');
    } catch (e) {
      _logger.e('Error removing reaction: $e');
      rethrow;
    }
  }

  /// Update message status
  Future<void> updateMessageStatus(
    String messageId,
    String chatId,
    MessageStatus status,
  ) async {
    try {
      final repository = _ref.read(messageRepositoryProvider);

      _logger.i('Updating message status: $messageId to ${status.value}');
      await repository.updateMessageStatus(messageId, status);

      // Invalidate related providers
      _ref.invalidate(messageByIdProvider(messageId));
      _ref.invalidate(chatMessagesProvider(chatId));

      _logger.i('Successfully updated message status: $messageId');
    } catch (e) {
      _logger.e('Error updating message status: $e');
      rethrow;
    }
  }
}

/// Provider for message mutations
final messageMutationProvider =
    StateNotifierProvider<MessageMutationNotifier, AsyncValue<void>>((ref) {
      return MessageMutationNotifier(ref);
    });

/// State provider for message composition
final messageCompositionProvider = StateProvider.family<String, String>(
  (ref, chatId) => '',
);

/// State provider for reply-to message
final replyToMessageProvider = StateProvider.family<MessageModel?, String>(
  (ref, chatId) => null,
);

/// State provider for message editing
final editingMessageProvider = StateProvider.family<MessageModel?, String>(
  (ref, chatId) => null,
);

/// Provider to check if current user can perform actions on a message
final messagePermissionsProvider =
    FutureProvider.family<Map<String, bool>, String>((ref, messageId) async {
      final message = await ref.watch(messageByIdProvider(messageId).future);
      final userId = ref.read(currentUserIdProvider);

      if (message == null) {
        return {
          'canEdit': false,
          'canDelete': false,
          'canReact': false,
          'canReply': false,
        };
      }

      final isOwner = message.senderId == userId;
      final chatPermissions = await ref.watch(
        chatPermissionsProvider(message.chatId).future,
      );

      return {
        'canEdit': isOwner && !message.isDeleted,
        'canDelete':
            isOwner || chatPermissions['canRemoveParticipants'] == true,
        'canReact': chatPermissions['canSendMessages'] == true,
        'canReply': chatPermissions['canSendMessages'] == true,
      };
    });

/// Provider to get message statistics for a chat
final messageStatsProvider = FutureProvider.family<Map<String, int>, String>((
  ref,
  chatId,
) async {
  final messages = await ref.watch(chatMessagesProvider(chatId).future);

  final stats = {
    'total': messages.length,
    'text': 0,
    'image': 0,
    'file': 0,
    'voice': 0,
    'system': 0,
    'link': 0,
  };

  for (final message in messages) {
    switch (message.type) {
      case MessageType.text:
        stats['text'] = (stats['text'] ?? 0) + 1;
        break;
      case MessageType.image:
        stats['image'] = (stats['image'] ?? 0) + 1;
        break;
      case MessageType.file:
        stats['file'] = (stats['file'] ?? 0) + 1;
        break;
      case MessageType.voice:
        stats['voice'] = (stats['voice'] ?? 0) + 1;
        break;
      case MessageType.system:
        stats['system'] = (stats['system'] ?? 0) + 1;
        break;
      case MessageType.link:
        stats['link'] = (stats['link'] ?? 0) + 1;
        break;
    }
  }

  _logger.i('Message stats for chat $chatId: $stats');
  return stats;
});
