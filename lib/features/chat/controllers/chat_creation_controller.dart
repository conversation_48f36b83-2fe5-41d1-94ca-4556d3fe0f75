import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../models/chat_models.dart';
import '../enums/chat_enums.dart';
import 'chat_controller.dart';

/// Logger for chat creation controller
final _logger = Logger();

/// Model for chat creation data
class ChatCreationData {
  final ChatType type;
  final String title;
  final List<String> participantIds;
  final String? classroomId;
  final ChatSettings? settings;
  final String? description;

  const ChatCreationData({
    required this.type,
    required this.title,
    required this.participantIds,
    this.classroomId,
    this.settings,
    this.description,
  });

  /// Validate chat creation data
  List<String> validate() {
    final errors = <String>[];

    if (title.trim().isEmpty) {
      errors.add('Chat title cannot be empty');
    }

    if (title.length > 100) {
      errors.add('Chat title cannot exceed 100 characters');
    }

    if (participantIds.isEmpty) {
      errors.add('At least one participant is required');
    }

    if (type == ChatType.oneToOne && participantIds.length != 1) {
      errors.add('One-to-one chat must have exactly one other participant');
    }

    if (type == ChatType.classroomDiscussion && classroomId == null) {
      errors.add('Classroom ID is required for classroom discussions');
    }

    final maxParticipants = settings?.maxParticipants;
    if (maxParticipants != null && participantIds.length > maxParticipants) {
      errors.add('Too many participants for this chat type');
    }

    return errors;
  }

  /// Check if data is valid
  bool get isValid => validate().isEmpty;
}

/// State notifier for chat creation
class ChatCreationNotifier extends StateNotifier<AsyncValue<ChatModel?>> {
  ChatCreationNotifier(this._ref) : super(const AsyncValue.data(null));

  final Ref _ref;

  /// Create a new chat
  Future<ChatModel> createChat(ChatCreationData data) async {
    state = const AsyncValue.loading();

    return await AsyncValue.guard(() async {
      // Validate data
      final validationErrors = data.validate();
      if (validationErrors.isNotEmpty) {
        throw Exception('Validation failed: ${validationErrors.join(', ')}');
      }

      final currentUserId = _ref.read(currentUserIdProvider);
      final chatMutation = _ref.read(chatMutationProvider.notifier);

      _logger.i('Creating ${data.type.value} chat: ${data.title}');

      // Create participants map
      final participants = <String, ChatParticipant>{};

      // Add current user as admin
      participants[currentUserId] = ChatParticipant(
        userId: currentUserId,
        userName: 'Current User', // TODO: Get from user profile
        role: ChatRole.admin,
        joinedAt: DateTime.now(),
        canSendMessages: true,
        canAddParticipants: true,
        isMuted: false,
      );

      // Add other participants
      for (final participantId in data.participantIds) {
        participants[participantId] = ChatParticipant(
          userId: participantId,
          userName: 'User $participantId', // TODO: Get from user profile
          role: ChatRole.participant,
          joinedAt: DateTime.now(),
          canSendMessages: true,
          canAddParticipants: false,
          isMuted: false,
        );
      }

      // Create chat model
      final chat = ChatModel(
        id: '', // Will be set by repository
        type: data.type,
        title: data.title,
        participantIds: [currentUserId, ...data.participantIds],
        participants: participants,
        classroomId: data.classroomId,
        settings:
            data.settings ??
            ChatSettings.getDefaultForChatType(data.type.value),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isActive: true,
        metadata: data.description != null
            ? {'description': data.description}
            : null,
      );

      final createdChat = await chatMutation.createChat(chat);

      _logger.i('Successfully created chat: ${createdChat.id}');
      return createdChat;
    }).then((result) {
      state = result.when(
        data: (chat) => AsyncValue.data(chat),
        loading: () => const AsyncValue.loading(),
        error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
      );

      return result.when(
        data: (chat) => chat,
        loading: () => throw Exception('Unexpected loading state'),
        error: (error, stackTrace) => throw error,
      );
    });
  }

  /// Create one-to-one chat
  Future<ChatModel> createOneToOneChat(
    String otherUserId, {
    String? title,
  }) async {
    final data = ChatCreationData(
      type: ChatType.oneToOne,
      title: title ?? 'Direct Message',
      participantIds: [otherUserId],
    );

    return await createChat(data);
  }

  /// Create group chat
  Future<ChatModel> createGroupChat(
    String title,
    List<String> participantIds, {
    String? description,
  }) async {
    final data = ChatCreationData(
      type: ChatType.groupChat,
      title: title,
      participantIds: participantIds,
      description: description,
    );

    return await createChat(data);
  }

  /// Create study group
  Future<ChatModel> createStudyGroup(
    String title,
    List<String> participantIds, {
    String? description,
  }) async {
    final data = ChatCreationData(
      type: ChatType.studyGroup,
      title: title,
      participantIds: participantIds,
      description: description,
      settings: ChatSettings.getDefaultForChatType('study_group'),
    );

    return await createChat(data);
  }

  /// Create classroom discussion
  Future<ChatModel> createClassroomDiscussion(
    String title,
    String classroomId,
    List<String> participantIds, {
    String? description,
  }) async {
    final data = ChatCreationData(
      type: ChatType.classroomDiscussion,
      title: title,
      participantIds: participantIds,
      classroomId: classroomId,
      description: description,
      settings: ChatSettings.getDefaultForChatType('classroom_discussion'),
    );

    return await createChat(data);
  }

  /// Clear creation state
  void clearState() {
    state = const AsyncValue.data(null);
  }
}

/// Provider for chat creation
final chatCreationProvider =
    StateNotifierProvider<ChatCreationNotifier, AsyncValue<ChatModel?>>((ref) {
      return ChatCreationNotifier(ref);
    });

/// State notifier for chat settings management
class ChatSettingsNotifier extends StateNotifier<AsyncValue<void>> {
  ChatSettingsNotifier(this._ref) : super(const AsyncValue.data(null));

  final Ref _ref;

  /// Update chat settings
  Future<void> updateChatSettings(
    String chatId,
    ChatSettings newSettings,
  ) async {
    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      final chatMutation = _ref.read(chatMutationProvider.notifier);

      _logger.i('Updating settings for chat: $chatId');

      await chatMutation.updateChat(chatId, {'settings': newSettings.toJson()});

      _logger.i('Successfully updated settings for chat: $chatId');
    });
  }

  /// Update specific setting
  Future<void> updateSetting(
    String chatId,
    String settingKey,
    dynamic value,
  ) async {
    final chat = await _ref.read(chatByIdProvider(chatId).future);
    if (chat == null) {
      throw Exception('Chat not found: $chatId');
    }

    final currentSettings = chat.settings;
    ChatSettings newSettings;

    switch (settingKey) {
      case 'allowAttachments':
        newSettings = currentSettings.copyWith(allowAttachments: value as bool);
        break;
      case 'allowVoiceMessages':
        newSettings = currentSettings.copyWith(
          allowVoiceMessages: value as bool,
        );
        break;
      case 'allowAddingParticipants':
        newSettings = currentSettings.copyWith(
          allowAddingParticipants: value as bool,
        );
        break;
      case 'allowParticipantsToLeave':
        newSettings = currentSettings.copyWith(
          allowParticipantsToLeave: value as bool,
        );
        break;
      case 'enableReactions':
        newSettings = currentSettings.copyWith(enableReactions: value as bool);
        break;
      case 'allowMessageEditing':
        newSettings = currentSettings.copyWith(
          allowMessageEditing: value as bool,
        );
        break;
      case 'allowMessageDeletion':
        newSettings = currentSettings.copyWith(
          allowMessageDeletion: value as bool,
        );
        break;
      case 'enableReadReceipts':
        newSettings = currentSettings.copyWith(
          enableReadReceipts: value as bool,
        );
        break;
      case 'adminOnlyMessages':
        newSettings = currentSettings.copyWith(
          adminOnlyMessages: value as bool,
        );
        break;
      case 'maxParticipants':
        newSettings = currentSettings.copyWith(maxParticipants: value as int?);
        break;
      default:
        throw Exception('Unknown setting: $settingKey');
    }

    await updateChatSettings(chatId, newSettings);
  }

  /// Toggle boolean setting
  Future<void> toggleSetting(String chatId, String settingKey) async {
    final chat = await _ref.read(chatByIdProvider(chatId).future);
    if (chat == null) {
      throw Exception('Chat not found: $chatId');
    }

    final currentSettings = chat.settings;
    bool currentValue;

    switch (settingKey) {
      case 'allowAttachments':
        currentValue = currentSettings.allowAttachments;
        break;
      case 'allowVoiceMessages':
        currentValue = currentSettings.allowVoiceMessages;
        break;
      case 'allowAddingParticipants':
        currentValue = currentSettings.allowAddingParticipants;
        break;
      case 'allowParticipantsToLeave':
        currentValue = currentSettings.allowParticipantsToLeave;
        break;
      case 'enableReactions':
        currentValue = currentSettings.enableReactions;
        break;
      case 'allowMessageEditing':
        currentValue = currentSettings.allowMessageEditing;
        break;
      case 'allowMessageDeletion':
        currentValue = currentSettings.allowMessageDeletion;
        break;
      case 'enableReadReceipts':
        currentValue = currentSettings.enableReadReceipts;
        break;
      case 'adminOnlyMessages':
        currentValue = currentSettings.adminOnlyMessages;
        break;
      default:
        throw Exception('Cannot toggle non-boolean setting: $settingKey');
    }

    await updateSetting(chatId, settingKey, !currentValue);
  }
}

/// Provider for chat settings management
final chatSettingsProvider =
    StateNotifierProvider<ChatSettingsNotifier, AsyncValue<void>>((ref) {
      return ChatSettingsNotifier(ref);
    });

/// Provider for chat creation validation
final chatCreationValidationProvider =
    Provider.family<List<String>, ChatCreationData>((ref, data) {
      return data.validate();
    });

/// Provider for chat type templates
final chatTypeTemplatesProvider = Provider<Map<ChatType, ChatCreationData>>((
  ref,
) {
  return {
    ChatType.oneToOne: ChatCreationData(
      type: ChatType.oneToOne,
      title: 'Direct Message',
      participantIds: [],
      settings: ChatSettings.getDefaultForChatType('one_to_one'),
    ),
    ChatType.groupChat: ChatCreationData(
      type: ChatType.groupChat,
      title: 'Group Chat',
      participantIds: [],
      settings: ChatSettings.getDefaultForChatType('group_chat'),
    ),
    ChatType.studyGroup: ChatCreationData(
      type: ChatType.studyGroup,
      title: 'Study Group',
      participantIds: [],
      settings: ChatSettings.getDefaultForChatType('study_group'),
    ),
    ChatType.classroomDiscussion: ChatCreationData(
      type: ChatType.classroomDiscussion,
      title: 'Classroom Discussion',
      participantIds: [],
      settings: ChatSettings.getDefaultForChatType('classroom_discussion'),
    ),
  };
});

/// Provider for chat creation utilities
final chatCreationUtilsProvider = Provider<ChatCreationUtils>((ref) {
  return ChatCreationUtils(ref);
});

/// Utility class for chat creation operations
class ChatCreationUtils {
  ChatCreationUtils(this._ref);

  final Ref _ref;

  /// Check if user can create chat of specific type
  bool canCreateChatType(ChatType type) {
    // In a real app, this would check user permissions
    // For now, allow all types
    return true;
  }

  /// Get recommended chat title based on participants
  String getRecommendedTitle(ChatType type, List<String> participantIds) {
    switch (type) {
      case ChatType.oneToOne:
        return 'Direct Message';
      case ChatType.groupChat:
        return 'Group Chat (${participantIds.length + 1} members)';
      case ChatType.studyGroup:
        return 'Study Group';
      case ChatType.classroomDiscussion:
        return 'Classroom Discussion';
      case ChatType.projectTeam:
        return 'Project Team';
      case ChatType.parentGroup:
        return 'Parent Group';
      case ChatType.teacherCircle:
        return 'Teacher Circle';
    }
  }

  /// Get maximum participants for chat type
  int? getMaxParticipants(ChatType type) {
    switch (type) {
      case ChatType.oneToOne:
        return 2;
      case ChatType.studyGroup:
        return 10;
      case ChatType.projectTeam:
        return 8;
      case ChatType.groupChat:
      case ChatType.classroomDiscussion:
      case ChatType.parentGroup:
      case ChatType.teacherCircle:
        return null; // No limit
    }
  }

  /// Check if chat already exists with same participants
  Future<ChatModel?> findExistingChat(
    ChatType type,
    List<String> participantIds,
  ) async {
    final currentUserId = _ref.read(currentUserIdProvider);
    final allParticipants = [currentUserId, ...participantIds]..sort();

    final userChats = await _ref.read(userChatsProvider.future);

    for (final chat in userChats) {
      if (chat.type == type) {
        final chatParticipants = List<String>.from(chat.participantIds)..sort();
        if (_listEquals(allParticipants, chatParticipants)) {
          return chat;
        }
      }
    }

    return null;
  }

  /// Helper method to compare lists
  bool _listEquals<T>(List<T> a, List<T> b) {
    if (a.length != b.length) return false;
    for (int i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }
    return true;
  }
}
