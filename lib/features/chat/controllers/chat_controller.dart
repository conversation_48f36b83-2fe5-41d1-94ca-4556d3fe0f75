import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../../../core/providers/auth_providers.dart';
import '../models/chat_models.dart';
import '../enums/chat_enums.dart';
import 'chat_repository_providers.dart';

/// Logger for chat controller
final _logger = Logger();

/// Provider for current user ID from authentication
final currentUserIdProvider = Provider<String>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.when(
    data: (state) => state.user?.id ?? 'XRTanMcAUWSMq3mrRvve2Y9IMP12', // Test user fallback
    loading: () => 'XRTanMcAUWSMq3mrRvve2Y9IMP12',
    error: (_, __) => 'XRTanMcAUWSMq3mrRvve2Y9IMP12',
  );
});

/// Provider to fetch all chats for the current user
final userChatsProvider = FutureProvider<List<ChatModel>>((ref) async {
  final repository = ref.read(chatRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);

  try {
    _logger.i('Fetching chats for current user: $userId');
    final chats = await repository.getChatsForUser(userId);
    _logger.i('Successfully fetched ${chats.length} chats for user $userId');
    return chats;
  } catch (e) {
    _logger.e('Error fetching user chats: $e');
    rethrow;
  }
});

/// Provider to fetch a specific chat by ID
final chatByIdProvider = FutureProvider.family<ChatModel?, String>((ref, chatId) async {
  final repository = ref.read(chatRepositoryProvider);

  try {
    _logger.i('Fetching chat by ID: $chatId');
    final chat = await repository.getChatById(chatId);
    if (chat != null) {
      _logger.i('Successfully fetched chat: ${chat.title}');
    } else {
      _logger.w('Chat not found: $chatId');
    }
    return chat;
  } catch (e) {
    _logger.e('Error fetching chat by ID: $e');
    rethrow;
  }
});

/// Provider to fetch chats by type for the current user
final chatsByTypeProvider = FutureProvider.family<List<ChatModel>, ChatType>((ref, type) async {
  final repository = ref.read(chatRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);

  try {
    _logger.i('Fetching ${type.value} chats for user: $userId');
    final chats = await repository.getChatsByType(userId, type);
    _logger.i('Successfully fetched ${chats.length} ${type.value} chats for user $userId');
    return chats;
  } catch (e) {
    _logger.e('Error fetching chats by type: $e');
    rethrow;
  }
});

/// Provider to fetch chats for a specific classroom
final classroomChatsProvider = FutureProvider.family<List<ChatModel>, String>((ref, classroomId) async {
  final repository = ref.read(chatRepositoryProvider);

  try {
    _logger.i('Fetching chats for classroom: $classroomId');
    final chats = await repository.getChatsForClassroom(classroomId);
    _logger.i('Successfully fetched ${chats.length} chats for classroom $classroomId');
    return chats;
  } catch (e) {
    _logger.e('Error fetching classroom chats: $e');
    rethrow;
  }
});

/// State provider for chat search query
final chatSearchQueryProvider = StateProvider<String>((ref) => '');

/// State provider for chat filter type
final chatFilterTypeProvider = StateProvider<ChatType?>((ref) => null);

/// Provider for filtered chats based on search and type filter
final filteredChatsProvider = FutureProvider<List<ChatModel>>((ref) async {
  final chats = await ref.watch(userChatsProvider.future);
  final searchQuery = ref.watch(chatSearchQueryProvider);
  final filterType = ref.watch(chatFilterTypeProvider);

  var filteredChats = chats;

  // Apply type filter
  if (filterType != null) {
    filteredChats = filteredChats.where((chat) => chat.type == filterType).toList();
  }

  // Apply search filter
  if (searchQuery.isNotEmpty) {
    filteredChats = filteredChats.where((chat) {
      return chat.title.toLowerCase().contains(searchQuery.toLowerCase()) ||
          chat.participantIds.any((id) => id.toLowerCase().contains(searchQuery.toLowerCase()));
    }).toList();
  }

  _logger.i('Filtered chats: ${filteredChats.length} results');
  return filteredChats;
});

/// State notifier for chat mutations (create, update, delete)
class ChatMutationNotifier extends StateNotifier<AsyncValue<void>> {
  ChatMutationNotifier(this._ref) : super(const AsyncValue.data(null));

  final Ref _ref;

  /// Create a new chat
  Future<ChatModel> createChat(ChatModel chat) async {
    state = const AsyncValue.loading();

    return await AsyncValue.guard(() async {
      final repository = _ref.read(chatRepositoryProvider);

      _logger.i('Creating new chat: ${chat.title}');
      final createdChat = await repository.createChat(chat);

      // Invalidate related providers
      _ref.invalidate(userChatsProvider);
      _ref.invalidate(chatsByTypeProvider);
      if (chat.classroomId != null) {
        _ref.invalidate(classroomChatsProvider(chat.classroomId!));
      }
      _ref.invalidate(filteredChatsProvider);

      _logger.i('Successfully created chat: ${createdChat.id}');
      return createdChat;
    }).then((result) {
      state = result.when(
        data: (_) => const AsyncValue.data(null),
        loading: () => const AsyncValue.loading(),
        error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
      );

      return result.when(
        data: (chat) => chat,
        loading: () => throw Exception('Unexpected loading state'),
        error: (error, stackTrace) => throw error,
      );
    });
  }

  /// Update chat information
  Future<ChatModel> updateChat(String chatId, Map<String, dynamic> updates) async {
    state = const AsyncValue.loading();

    return await AsyncValue.guard(() async {
      final repository = _ref.read(chatRepositoryProvider);

      _logger.i('Updating chat: $chatId');
      final updatedChat = await repository.updateChat(chatId, updates);

      // Invalidate related providers
      _ref.invalidate(userChatsProvider);
      _ref.invalidate(chatByIdProvider(chatId));
      _ref.invalidate(chatsByTypeProvider);
      _ref.invalidate(classroomChatsProvider);
      _ref.invalidate(filteredChatsProvider);

      _logger.i('Successfully updated chat: $chatId');
      return updatedChat;
    }).then((result) {
      state = result.when(
        data: (_) => const AsyncValue.data(null),
        loading: () => const AsyncValue.loading(),
        error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
      );

      return result.when(
        data: (chat) => chat,
        loading: () => throw Exception('Unexpected loading state'),
        error: (error, stackTrace) => throw error,
      );
    });
  }

  /// Delete a chat
  Future<void> deleteChat(String chatId) async {
    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      final repository = _ref.read(chatRepositoryProvider);

      _logger.i('Deleting chat: $chatId');
      await repository.deleteChat(chatId);

      // Invalidate related providers
      _ref.invalidate(userChatsProvider);
      _ref.invalidate(chatByIdProvider(chatId));
      _ref.invalidate(chatsByTypeProvider);
      _ref.invalidate(classroomChatsProvider);
      _ref.invalidate(filteredChatsProvider);

      _logger.i('Successfully deleted chat: $chatId');
    });
  }

  /// Update last message for a chat
  Future<void> updateLastMessage(
    String chatId,
    String messageId,
    DateTime timestamp,
  ) async {
    try {
      final repository = _ref.read(chatRepositoryProvider);

      _logger.i('Updating last message for chat: $chatId');
      await repository.updateLastMessage(chatId, messageId, timestamp);

      // Invalidate related providers to refresh UI
      _ref.invalidate(userChatsProvider);
      _ref.invalidate(chatByIdProvider(chatId));
      _ref.invalidate(filteredChatsProvider);

      _logger.i('Successfully updated last message for chat: $chatId');
    } catch (e) {
      _logger.e('Error updating last message: $e');
      rethrow;
    }
  }
}

/// Provider for chat mutations
final chatMutationProvider = StateNotifierProvider<ChatMutationNotifier, AsyncValue<void>>((ref) {
  return ChatMutationNotifier(ref);
});

/// Helper provider to check if current user can perform actions on a chat
final chatPermissionsProvider = FutureProvider.family<Map<String, bool>, String>((ref, chatId) async {
  final chat = await ref.watch(chatByIdProvider(chatId).future);
  final userId = ref.read(currentUserIdProvider);

  if (chat == null) {
    return {
      'canSendMessages': false,
      'canAddParticipants': false,
      'canRemoveParticipants': false,
      'canEditSettings': false,
      'canDeleteChat': false,
    };
  }

  final participant = chat.participants[userId];
  if (participant == null) {
    return {
      'canSendMessages': false,
      'canAddParticipants': false,
      'canRemoveParticipants': false,
      'canEditSettings': false,
      'canDeleteChat': false,
    };
  }

  final isAdmin = participant.role == ChatRole.admin;
  final isModerator = participant.role == ChatRole.moderator;

  return {
    'canSendMessages': participant.canSendMessages,
    'canAddParticipants': participant.canAddParticipants || isAdmin || isModerator,
    'canRemoveParticipants': isAdmin || isModerator,
    'canEditSettings': isAdmin,
    'canDeleteChat': isAdmin,
  };
});

/// Provider to get unread message count for current user
final unreadMessageCountProvider = FutureProvider<int>((ref) async {
  final chats = await ref.watch(userChatsProvider.future);
  final userId = ref.read(currentUserIdProvider);

  int totalUnread = 0;

  for (final chat in chats) {
    final participant = chat.participants[userId];
    if (participant != null && chat.lastMessageAt != null) {
      final lastReadAt = participant.lastReadAt;
      if (lastReadAt == null || chat.lastMessageAt!.isAfter(lastReadAt)) {
        totalUnread += chat.unreadCount;
      }
    }
  }

  _logger.i('Total unread messages for user $userId: $totalUnread');
  return totalUnread;
});
