import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../models/chat_models.dart';
import '../enums/chat_enums.dart';
import 'chat_controller.dart';
import 'message_controller.dart';
import 'participant_controller.dart';

/// Logger for message status controller
final _logger = Logger();

/// State notifier for managing message read status
class MessageReadStatusNotifier extends StateNotifier<AsyncValue<void>> {
  MessageReadStatusNotifier(this._ref) : super(const AsyncValue.data(null));

  final Ref _ref;

  /// Mark messages as read for current user in a chat
  Future<void> markMessagesAsRead(
    String chatId,
    List<String> messageIds,
  ) async {
    if (messageIds.isEmpty) return;

    try {
      final userId = _ref.read(currentUserIdProvider);
      final messageMutation = _ref.read(messageMutationProvider.notifier);
      final participantMutation = _ref.read(
        participantMutationProvider.notifier,
      );

      _logger.i(
        'Marking ${messageIds.length} messages as read for user $userId in chat $chatId',
      );

      // Update message status to read
      for (final messageId in messageIds) {
        await messageMutation.updateMessageStatus(
          messageId,
          chatId,
          MessageStatus.read,
        );
      }

      // Update participant's last read timestamp
      await participantMutation.updateLastReadAt(
        chatId,
        userId,
        DateTime.now(),
      );

      _logger.i(
        'Successfully marked messages as read for user $userId in chat $chatId',
      );
    } catch (e) {
      _logger.e('Error marking messages as read: $e');
      rethrow;
    }
  }

  /// Mark all messages in a chat as read for current user
  Future<void> markAllMessagesAsRead(String chatId) async {
    try {
      final userId = _ref.read(currentUserIdProvider);
      final messages = await _ref.read(chatMessagesProvider(chatId).future);
      final participantMutation = _ref.read(
        participantMutationProvider.notifier,
      );

      _logger.i(
        'Marking all messages as read for user $userId in chat $chatId',
      );

      // Get unread message IDs
      final unreadMessageIds = messages
          .where(
            (message) =>
                message.status != MessageStatus.read &&
                message.senderId != userId,
          )
          .map((message) => message.id)
          .toList();

      if (unreadMessageIds.isNotEmpty) {
        await markMessagesAsRead(chatId, unreadMessageIds);
      }

      // Update participant's last read timestamp
      await participantMutation.updateLastReadAt(
        chatId,
        userId,
        DateTime.now(),
      );

      _logger.i(
        'Successfully marked all messages as read for user $userId in chat $chatId',
      );
    } catch (e) {
      _logger.e('Error marking all messages as read: $e');
      rethrow;
    }
  }
}

/// Provider for message read status management
final messageReadStatusProvider =
    StateNotifierProvider<MessageReadStatusNotifier, AsyncValue<void>>((ref) {
      return MessageReadStatusNotifier(ref);
    });

/// Provider to get unread message count for a specific chat
final chatUnreadCountProvider = FutureProvider.family<int, String>((
  ref,
  chatId,
) async {
  final messages = await ref.watch(chatMessagesProvider(chatId).future);
  final userId = ref.read(currentUserIdProvider);
  final participant = await ref.watch(
    participantProvider({'chatId': chatId, 'userId': userId}).future,
  );

  if (participant == null) return 0;

  final lastReadAt = participant.lastReadAt;
  if (lastReadAt == null) {
    // If never read, count all messages from others
    return messages.where((message) => message.senderId != userId).length;
  }

  // Count messages sent after last read time
  final unreadCount = messages
      .where(
        (message) =>
            message.senderId != userId && message.sentAt.isAfter(lastReadAt),
      )
      .length;

  _logger.d('Unread count for chat $chatId: $unreadCount');
  return unreadCount;
});

/// Provider to check if a specific message is read by current user
final messageReadByCurrentUserProvider = FutureProvider.family<bool, String>((
  ref,
  messageId,
) async {
  final message = await ref.watch(messageByIdProvider(messageId).future);
  final userId = ref.read(currentUserIdProvider);

  if (message == null) return false;

  // Own messages are always considered "read"
  if (message.senderId == userId) return true;

  // Check if message status is read
  return message.status == MessageStatus.read;
});

/// State notifier for managing message delivery status
class MessageDeliveryNotifier extends StateNotifier<AsyncValue<void>> {
  MessageDeliveryNotifier(this._ref) : super(const AsyncValue.data(null));

  final Ref _ref;

  /// Update message delivery status
  Future<void> updateDeliveryStatus(
    String messageId,
    String chatId,
    MessageStatus status,
  ) async {
    try {
      final messageMutation = _ref.read(messageMutationProvider.notifier);

      _logger.i(
        'Updating delivery status for message $messageId to ${status.value}',
      );
      await messageMutation.updateMessageStatus(messageId, chatId, status);

      _logger.i('Successfully updated delivery status for message $messageId');
    } catch (e) {
      _logger.e('Error updating delivery status: $e');
      rethrow;
    }
  }

  /// Mark message as delivered to all participants
  Future<void> markAsDelivered(String messageId, String chatId) async {
    await updateDeliveryStatus(messageId, chatId, MessageStatus.delivered);
  }

  /// Mark message as failed to deliver
  Future<void> markAsFailed(String messageId, String chatId) async {
    await updateDeliveryStatus(messageId, chatId, MessageStatus.failed);
  }
}

/// Provider for message delivery management
final messageDeliveryProvider =
    StateNotifierProvider<MessageDeliveryNotifier, AsyncValue<void>>((ref) {
      return MessageDeliveryNotifier(ref);
    });

/// Provider to get message delivery statistics for a chat
final messageDeliveryStatsProvider =
    FutureProvider.family<Map<String, int>, String>((ref, chatId) async {
      final messages = await ref.watch(chatMessagesProvider(chatId).future);
      final userId = ref.read(currentUserIdProvider);

      // Only count messages sent by current user
      final userMessages = messages.where(
        (message) => message.senderId == userId,
      );

      final stats = {
        'total': userMessages.length,
        'sending': 0,
        'sent': 0,
        'delivered': 0,
        'read': 0,
        'failed': 0,
      };

      for (final message in userMessages) {
        switch (message.status) {
          case MessageStatus.sending:
            stats['sending'] = (stats['sending'] ?? 0) + 1;
            break;
          case MessageStatus.sent:
            stats['sent'] = (stats['sent'] ?? 0) + 1;
            break;
          case MessageStatus.delivered:
            stats['delivered'] = (stats['delivered'] ?? 0) + 1;
            break;
          case MessageStatus.read:
            stats['read'] = (stats['read'] ?? 0) + 1;
            break;
          case MessageStatus.failed:
            stats['failed'] = (stats['failed'] ?? 0) + 1;
            break;
        }
      }

      _logger.d('Message delivery stats for chat $chatId: $stats');
      return stats;
    });

/// State notifier for managing message retry functionality
class MessageRetryNotifier extends StateNotifier<AsyncValue<void>> {
  MessageRetryNotifier(this._ref) : super(const AsyncValue.data(null));

  final Ref _ref;

  /// Retry sending a failed message
  Future<void> retryMessage(String messageId, String chatId) async {
    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      final message = await _ref.read(messageByIdProvider(messageId).future);
      if (message == null) {
        throw Exception('Message not found: $messageId');
      }

      if (message.status != MessageStatus.failed) {
        throw Exception('Message is not in failed state');
      }

      _logger.i('Retrying failed message: $messageId');

      // Update status to sending
      final messageDelivery = _ref.read(messageDeliveryProvider.notifier);
      await messageDelivery.updateDeliveryStatus(
        messageId,
        chatId,
        MessageStatus.sending,
      );

      // Simulate retry logic (in real app, this would re-attempt the send operation)
      await Future.delayed(const Duration(seconds: 1));

      // Mark as sent (in real app, this would depend on actual send result)
      await messageDelivery.updateDeliveryStatus(
        messageId,
        chatId,
        MessageStatus.sent,
      );

      _logger.i('Successfully retried message: $messageId');
    });
  }

  /// Retry all failed messages in a chat
  Future<void> retryAllFailedMessages(String chatId) async {
    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      final messages = await _ref.read(chatMessagesProvider(chatId).future);
      final userId = _ref.read(currentUserIdProvider);

      final failedMessages = messages
          .where(
            (message) =>
                message.senderId == userId &&
                message.status == MessageStatus.failed,
          )
          .toList();

      if (failedMessages.isEmpty) {
        _logger.i('No failed messages to retry in chat $chatId');
        return;
      }

      _logger.i(
        'Retrying ${failedMessages.length} failed messages in chat $chatId',
      );

      for (final message in failedMessages) {
        try {
          await retryMessage(message.id, chatId);
        } catch (e) {
          _logger.e('Error retrying message ${message.id}: $e');
        }
      }

      _logger.i('Completed retry for all failed messages in chat $chatId');
    });
  }
}

/// Provider for message retry functionality
final messageRetryProvider =
    StateNotifierProvider<MessageRetryNotifier, AsyncValue<void>>((ref) {
      return MessageRetryNotifier(ref);
    });

/// Provider to get failed messages for a chat
final failedMessagesProvider =
    FutureProvider.family<List<MessageModel>, String>((ref, chatId) async {
      final messages = await ref.watch(chatMessagesProvider(chatId).future);
      final userId = ref.read(currentUserIdProvider);

      final failedMessages = messages
          .where(
            (message) =>
                message.senderId == userId &&
                message.status == MessageStatus.failed,
          )
          .toList();

      _logger.d('Failed messages for chat $chatId: ${failedMessages.length}');
      return failedMessages;
    });

/// Provider for automatic message status updates
final messageStatusAutoUpdaterProvider =
    Provider.family<MessageStatusAutoUpdater, String>((ref, chatId) {
      return MessageStatusAutoUpdater(ref, chatId);
    });

/// Class for handling automatic message status updates
class MessageStatusAutoUpdater {
  MessageStatusAutoUpdater(this._ref, this._chatId);

  final Ref _ref;
  final String _chatId;
  Timer? _statusUpdateTimer;

  /// Start automatic status updates
  void startAutoUpdates() {
    _statusUpdateTimer?.cancel();
    _statusUpdateTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      _updateMessageStatuses();
    });
    _logger.d('Started auto status updates for chat $_chatId');
  }

  /// Stop automatic status updates
  void stopAutoUpdates() {
    _statusUpdateTimer?.cancel();
    _statusUpdateTimer = null;
    _logger.d('Stopped auto status updates for chat $_chatId');
  }

  /// Update message statuses
  Future<void> _updateMessageStatuses() async {
    try {
      final messages = await _ref.read(chatMessagesProvider(_chatId).future);
      final userId = _ref.read(currentUserIdProvider);
      final messageDelivery = _ref.read(messageDeliveryProvider.notifier);

      // Update sending messages to sent after some time
      final sendingMessages = messages
          .where(
            (message) =>
                message.senderId == userId &&
                message.status == MessageStatus.sending &&
                DateTime.now().difference(message.sentAt).inSeconds > 5,
          )
          .toList();

      for (final message in sendingMessages) {
        await messageDelivery.markAsDelivered(message.id, _chatId);
      }

      if (sendingMessages.isNotEmpty) {
        _logger.d(
          'Auto-updated ${sendingMessages.length} message statuses in chat $_chatId',
        );
      }
    } catch (e) {
      _logger.e('Error in auto status update for chat $_chatId: $e');
    }
  }

  void dispose() {
    stopAutoUpdates();
  }
}
