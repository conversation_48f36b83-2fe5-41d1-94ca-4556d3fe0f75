import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../models/chat_models.dart';
import '../enums/chat_enums.dart';
import 'chat_controller.dart';
import 'participant_controller.dart';
import 'message_management_controller.dart';

/// Logger for participant management controller
final _logger = Logger();

/// State notifier for managing participant invitations
class ParticipantInvitationNotifier extends StateNotifier<AsyncValue<void>> {
  ParticipantInvitationNotifier(this._ref) : super(const AsyncValue.data(null));

  final Ref _ref;

  /// Invite users to a chat
  Future<void> inviteParticipants(
    String chatId,
    List<String> userIds,
    ChatRole role,
  ) async {
    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      final participantMutation = _ref.read(
        participantMutationProvider.notifier,
      );
      final messageComposer = _ref.read(
        messageComposerProvider(chatId).notifier,
      );

      _logger.i('Inviting ${userIds.length} participants to chat: $chatId');

      for (final userId in userIds) {
        try {
          // Create participant
          final participant = ChatParticipant(
            userId: userId,
            userName: 'User $userId', // TODO: Get from user profile
            role: role,
            joinedAt: DateTime.now(),
            canSendMessages: true,
            canAddParticipants:
                role == ChatRole.admin || role == ChatRole.moderator,
            isMuted: false,
          );

          await participantMutation.addParticipant(chatId, participant);

          // Send system message about the invitation
          await messageComposer.sendSystemMessage('$userId joined the chat');

          _logger.i('Successfully invited participant: $userId');
        } catch (e) {
          _logger.e('Error inviting participant $userId: $e');
          rethrow;
        }
      }

      _logger.i('Successfully invited all participants to chat: $chatId');
    });
  }

  /// Remove participants from a chat
  Future<void> removeParticipants(String chatId, List<String> userIds) async {
    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      final participantMutation = _ref.read(
        participantMutationProvider.notifier,
      );
      final messageComposer = _ref.read(
        messageComposerProvider(chatId).notifier,
      );

      _logger.i('Removing ${userIds.length} participants from chat: $chatId');

      for (final userId in userIds) {
        try {
          await participantMutation.removeParticipant(chatId, userId);

          // Send system message about the removal
          await messageComposer.sendSystemMessage('$userId left the chat');

          _logger.i('Successfully removed participant: $userId');
        } catch (e) {
          _logger.e('Error removing participant $userId: $e');
          rethrow;
        }
      }

      _logger.i('Successfully removed all participants from chat: $chatId');
    });
  }
}

/// Provider for participant invitations
final participantInvitationProvider =
    StateNotifierProvider<ParticipantInvitationNotifier, AsyncValue<void>>((
      ref,
    ) {
      return ParticipantInvitationNotifier(ref);
    });

/// State notifier for managing participant roles and permissions
class ParticipantRoleManagerNotifier extends StateNotifier<AsyncValue<void>> {
  ParticipantRoleManagerNotifier(this._ref)
    : super(const AsyncValue.data(null));

  final Ref _ref;

  /// Promote participant to a higher role
  Future<void> promoteParticipant(
    String chatId,
    String userId,
    ChatRole newRole,
  ) async {
    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      final participantMutation = _ref.read(
        participantMutationProvider.notifier,
      );
      final messageComposer = _ref.read(
        messageComposerProvider(chatId).notifier,
      );

      _logger.i(
        'Promoting participant $userId to ${newRole.value} in chat: $chatId',
      );

      await participantMutation.updateParticipantRole(chatId, userId, newRole);

      // Send system message about the promotion
      await messageComposer.sendSystemMessage(
        '$userId was promoted to ${newRole.displayName}',
      );

      _logger.i('Successfully promoted participant: $userId');
    });
  }

  /// Demote participant to a lower role
  Future<void> demoteParticipant(
    String chatId,
    String userId,
    ChatRole newRole,
  ) async {
    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      final participantMutation = _ref.read(
        participantMutationProvider.notifier,
      );
      final messageComposer = _ref.read(
        messageComposerProvider(chatId).notifier,
      );

      _logger.i(
        'Demoting participant $userId to ${newRole.value} in chat: $chatId',
      );

      await participantMutation.updateParticipantRole(chatId, userId, newRole);

      // Send system message about the demotion
      await messageComposer.sendSystemMessage(
        '$userId was changed to ${newRole.displayName}',
      );

      _logger.i('Successfully demoted participant: $userId');
    });
  }

  /// Update participant permissions
  Future<void> updatePermissions(
    String chatId,
    String userId,
    Map<String, bool> permissions,
  ) async {
    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      final participantMutation = _ref.read(
        participantMutationProvider.notifier,
      );

      _logger.i(
        'Updating permissions for participant $userId in chat: $chatId',
      );

      await participantMutation.updateParticipantPermissions(
        chatId,
        userId,
        permissions,
      );

      _logger.i('Successfully updated permissions for participant: $userId');
    });
  }

  /// Mute participant
  Future<void> muteParticipant(String chatId, String userId) async {
    await updatePermissions(chatId, userId, {'canSendMessages': false});

    final messageComposer = _ref.read(messageComposerProvider(chatId).notifier);
    await messageComposer.sendSystemMessage('$userId was muted');

    _logger.i('Muted participant: $userId in chat: $chatId');
  }

  /// Unmute participant
  Future<void> unmuteParticipant(String chatId, String userId) async {
    await updatePermissions(chatId, userId, {'canSendMessages': true});

    final messageComposer = _ref.read(messageComposerProvider(chatId).notifier);
    await messageComposer.sendSystemMessage('$userId was unmuted');

    _logger.i('Unmuted participant: $userId in chat: $chatId');
  }
}

/// Provider for participant role management
final participantRoleManagerProvider =
    StateNotifierProvider<ParticipantRoleManagerNotifier, AsyncValue<void>>((
      ref,
    ) {
      return ParticipantRoleManagerNotifier(ref);
    });

/// Provider for participant discovery (finding users to invite)
final participantDiscoveryProvider =
    FutureProvider.family<List<String>, Map<String, String>>((
      ref,
      params,
    ) async {
      final searchQuery = params['searchQuery'] ?? '';

      // In a real app, this would search through user profiles
      // For now, we'll return mock user IDs
      await Future.delayed(
        const Duration(milliseconds: 500),
      ); // Simulate network delay

      final mockUsers = [
        'user_teacher_1',
        'user_student_1',
        'user_student_2',
        'user_student_3',
        'user_parent_1',
        'user_parent_2',
      ];

      if (searchQuery.isEmpty) {
        return mockUsers;
      }

      final filteredUsers = mockUsers
          .where(
            (userId) =>
                userId.toLowerCase().contains(searchQuery.toLowerCase()),
          )
          .toList();

      _logger.i('Found ${filteredUsers.length} users matching "$searchQuery"');
      return filteredUsers;
    });

/// Provider for participant activity tracking
final participantActivityProvider =
    FutureProvider.family<Map<String, DateTime>, String>((ref, chatId) async {
      final participants = await ref.watch(
        chatParticipantsProvider(chatId).future,
      );

      final activity = <String, DateTime>{};

      for (final participant in participants) {
        // Use last read time as activity indicator
        activity[participant.userId] =
            participant.lastReadAt ?? participant.joinedAt;
      }

      _logger.d(
        'Tracked activity for ${activity.length} participants in chat $chatId',
      );
      return activity;
    });

/// Provider for participant management utilities
final participantManagementUtilsProvider =
    Provider.family<ParticipantManagementUtils, String>((ref, chatId) {
      return ParticipantManagementUtils(ref, chatId);
    });

/// Utility class for participant management operations
class ParticipantManagementUtils {
  ParticipantManagementUtils(this._ref, this._chatId);

  final Ref _ref;
  final String _chatId;

  /// Check if current user can manage participants
  Future<bool> canManageParticipants() async {
    final permissions = await _ref.read(
      currentUserPermissionsProvider(_chatId).future,
    );
    return permissions['canAddParticipants'] == true ||
        permissions['canRemoveParticipants'] == true;
  }

  /// Check if current user can manage roles
  Future<bool> canManageRoles() async {
    final permissions = await _ref.read(
      currentUserPermissionsProvider(_chatId).future,
    );
    return permissions['canEditSettings'] == true;
  }

  /// Get participants that can be promoted
  Future<List<ChatParticipant>> getPromotableParticipants() async {
    final participants = await _ref.read(
      chatParticipantsProvider(_chatId).future,
    );
    final currentUserId = _ref.read(currentUserIdProvider);

    return participants
        .where(
          (participant) =>
              participant.userId != currentUserId &&
              participant.role != ChatRole.admin,
        )
        .toList();
  }

  /// Get participants that can be demoted
  Future<List<ChatParticipant>> getDemotableParticipants() async {
    final participants = await _ref.read(
      chatParticipantsProvider(_chatId).future,
    );
    final currentUserId = _ref.read(currentUserIdProvider);

    return participants
        .where(
          (participant) =>
              participant.userId != currentUserId &&
              participant.role != ChatRole.participant,
        )
        .toList();
  }

  /// Get inactive participants (haven't been active recently)
  Future<List<ChatParticipant>> getInactiveParticipants({
    int dayThreshold = 7,
  }) async {
    final participants = await _ref.read(
      chatParticipantsProvider(_chatId).future,
    );
    final now = DateTime.now();

    return participants.where((participant) {
      final lastActivity = participant.lastReadAt ?? participant.joinedAt;
      return now.difference(lastActivity).inDays > dayThreshold;
    }).toList();
  }

  /// Get participant count by role
  Future<Map<ChatRole, int>> getParticipantCountByRole() async {
    final participants = await _ref.read(
      chatParticipantsProvider(_chatId).future,
    );

    final counts = <ChatRole, int>{
      ChatRole.admin: 0,
      ChatRole.moderator: 0,
      ChatRole.participant: 0,
      ChatRole.observer: 0,
    };

    for (final participant in participants) {
      counts[participant.role] = (counts[participant.role] ?? 0) + 1;
    }

    return counts;
  }

  /// Validate participant limits for chat type
  Future<bool> canAddMoreParticipants(int additionalCount) async {
    final chat = await _ref.read(chatByIdProvider(_chatId).future);
    if (chat == null) return false;

    final currentParticipants = await _ref.read(
      chatParticipantsProvider(_chatId).future,
    );
    final currentCount = currentParticipants.length;
    final newTotal = currentCount + additionalCount;

    final maxParticipants = chat.settings.maxParticipants;
    if (maxParticipants != null && newTotal > maxParticipants) {
      return false;
    }

    return true;
  }

  /// Get suggested participants based on chat context
  Future<List<String>> getSuggestedParticipants() async {
    final chat = await _ref.read(chatByIdProvider(_chatId).future);
    if (chat == null) return [];

    // For classroom chats, suggest classmates
    if (chat.classroomId != null) {
      // In a real app, this would fetch classmates from the classroom
      return ['classmate_1', 'classmate_2', 'classmate_3'];
    }

    // For other chats, suggest recent contacts
    return ['recent_contact_1', 'recent_contact_2'];
  }
}
