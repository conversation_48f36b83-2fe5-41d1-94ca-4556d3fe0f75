import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../models/chat_models.dart';
import '../enums/chat_enums.dart';
import 'chat_controller.dart';
import 'message_controller.dart';

/// Logger for message management controller
final _logger = Logger();

/// State notifier for managing message composition and sending
class MessageComposerNotifier extends StateNotifier<AsyncValue<void>> {
  MessageComposerNotifier(this._ref, this._chatId)
    : super(const AsyncValue.data(null));

  final Ref _ref;
  final String _chatId;

  /// Send a text message
  Future<MessageModel> sendTextMessage(String content) async {
    if (content.trim().isEmpty) {
      throw Exception('Message content cannot be empty');
    }

    state = const AsyncValue.loading();

    return await AsyncValue.guard(() async {
      final userId = _ref.read(currentUserIdProvider);
      final messageMutation = _ref.read(messageMutationProvider.notifier);

      final message = MessageModel(
        id: '', // Will be set by repository
        chatId: _chatId,
        senderId: userId,
        senderName: 'Current User', // TODO: Get from user profile
        type: MessageType.text,
        content: content.trim(),
        attachments: [],
        reactions: {},
        status: MessageStatus.sending,
        sentAt: DateTime.now(),
        isDeleted: false,
      );

      _logger.i('Composing text message for chat: $_chatId');
      final sentMessage = await messageMutation.sendMessage(message);

      _logger.i('Successfully sent text message: ${sentMessage.id}');
      return sentMessage;
    }).then((result) {
      state = result.when(
        data: (_) => const AsyncValue.data(null),
        loading: () => const AsyncValue.loading(),
        error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
      );

      return result.when(
        data: (message) => message,
        loading: () => throw Exception('Unexpected loading state'),
        error: (error, stackTrace) => throw error,
      );
    });
  }

  /// Send a reply message
  Future<MessageModel> sendReplyMessage(
    String content,
    String replyToMessageId,
  ) async {
    if (content.trim().isEmpty) {
      throw Exception('Reply content cannot be empty');
    }

    state = const AsyncValue.loading();

    return await AsyncValue.guard(() async {
      final userId = _ref.read(currentUserIdProvider);
      final messageMutation = _ref.read(messageMutationProvider.notifier);

      final message = MessageModel(
        id: '', // Will be set by repository
        chatId: _chatId,
        senderId: userId,
        senderName: 'Current User', // TODO: Get from user profile
        type: MessageType.text,
        content: content.trim(),
        attachments: [],
        replyToMessageId: replyToMessageId,
        reactions: {},
        status: MessageStatus.sending,
        sentAt: DateTime.now(),
        isDeleted: false,
      );

      _logger.i('Composing reply message for chat: $_chatId');
      final sentMessage = await messageMutation.sendMessage(message);

      _logger.i('Successfully sent reply message: ${sentMessage.id}');
      return sentMessage;
    }).then((result) {
      state = result.when(
        data: (_) => const AsyncValue.data(null),
        loading: () => const AsyncValue.loading(),
        error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
      );

      return result.when(
        data: (message) => message,
        loading: () => throw Exception('Unexpected loading state'),
        error: (error, stackTrace) => throw error,
      );
    });
  }

  /// Send a system message (for notifications, etc.)
  Future<MessageModel> sendSystemMessage(String content) async {
    state = const AsyncValue.loading();

    return await AsyncValue.guard(() async {
      final messageMutation = _ref.read(messageMutationProvider.notifier);

      final message = MessageModel(
        id: '', // Will be set by repository
        chatId: _chatId,
        senderId: 'system',
        senderName: 'System',
        type: MessageType.system,
        content: content,
        attachments: [],
        reactions: {},
        status: MessageStatus.sent,
        sentAt: DateTime.now(),
        isDeleted: false,
      );

      _logger.i('Composing system message for chat: $_chatId');
      final sentMessage = await messageMutation.sendMessage(message);

      _logger.i('Successfully sent system message: ${sentMessage.id}');
      return sentMessage;
    }).then((result) {
      state = result.when(
        data: (_) => const AsyncValue.data(null),
        loading: () => const AsyncValue.loading(),
        error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
      );

      return result.when(
        data: (message) => message,
        loading: () => throw Exception('Unexpected loading state'),
        error: (error, stackTrace) => throw error,
      );
    });
  }
}

/// Provider for message composer
final messageComposerProvider =
    StateNotifierProvider.family<
      MessageComposerNotifier,
      AsyncValue<void>,
      String
    >((ref, chatId) {
      return MessageComposerNotifier(ref, chatId);
    });

/// State notifier for managing message drafts
class MessageDraftNotifier extends StateNotifier<Map<String, String>> {
  MessageDraftNotifier() : super({});

  /// Save draft for a chat
  void saveDraft(String chatId, String content) {
    state = {...state, chatId: content};
    _logger.d('Saved draft for chat $chatId: ${content.length} characters');
  }

  /// Get draft for a chat
  String getDraft(String chatId) {
    return state[chatId] ?? '';
  }

  /// Clear draft for a chat
  void clearDraft(String chatId) {
    final newState = Map<String, String>.from(state);
    newState.remove(chatId);
    state = newState;
    _logger.d('Cleared draft for chat $chatId');
  }

  /// Clear all drafts
  void clearAllDrafts() {
    state = {};
    _logger.d('Cleared all drafts');
  }
}

/// Provider for message drafts
final messageDraftProvider =
    StateNotifierProvider<MessageDraftNotifier, Map<String, String>>((ref) {
      return MessageDraftNotifier();
    });

/// State notifier for managing typing indicators
class TypingIndicatorNotifier extends StateNotifier<Map<String, Set<String>>> {
  TypingIndicatorNotifier() : super({});

  Timer? _typingTimer;

  /// Start typing indicator for a chat
  void startTyping(String chatId, String userId) {
    final currentTyping = state[chatId] ?? <String>{};
    final newTyping = Set<String>.from(currentTyping)..add(userId);

    state = {...state, chatId: newTyping};
    _logger.d('User $userId started typing in chat $chatId');

    // Auto-stop typing after 3 seconds
    _typingTimer?.cancel();
    _typingTimer = Timer(const Duration(seconds: 3), () {
      stopTyping(chatId, userId);
    });
  }

  /// Stop typing indicator for a chat
  void stopTyping(String chatId, String userId) {
    final currentTyping = state[chatId] ?? <String>{};
    final newTyping = Set<String>.from(currentTyping)..remove(userId);

    if (newTyping.isEmpty) {
      final newState = Map<String, Set<String>>.from(state);
      newState.remove(chatId);
      state = newState;
    } else {
      state = {...state, chatId: newTyping};
    }

    _logger.d('User $userId stopped typing in chat $chatId');
    _typingTimer?.cancel();
  }

  /// Get typing users for a chat
  Set<String> getTypingUsers(String chatId) {
    return state[chatId] ?? <String>{};
  }

  @override
  void dispose() {
    _typingTimer?.cancel();
    super.dispose();
  }
}

/// Provider for typing indicators
final typingIndicatorProvider =
    StateNotifierProvider<TypingIndicatorNotifier, Map<String, Set<String>>>((
      ref,
    ) {
      return TypingIndicatorNotifier();
    });

/// Provider to get typing users for a specific chat
final chatTypingUsersProvider = Provider.family<Set<String>, String>((
  ref,
  chatId,
) {
  final typingState = ref.watch(typingIndicatorProvider);
  return typingState[chatId] ?? <String>{};
});

/// State notifier for managing message selection (for bulk operations)
class MessageSelectionNotifier extends StateNotifier<Map<String, Set<String>>> {
  MessageSelectionNotifier() : super({});

  /// Toggle message selection
  void toggleSelection(String chatId, String messageId) {
    final currentSelection = state[chatId] ?? <String>{};
    final newSelection = Set<String>.from(currentSelection);

    if (newSelection.contains(messageId)) {
      newSelection.remove(messageId);
    } else {
      newSelection.add(messageId);
    }

    if (newSelection.isEmpty) {
      final newState = Map<String, Set<String>>.from(state);
      newState.remove(chatId);
      state = newState;
    } else {
      state = {...state, chatId: newSelection};
    }

    _logger.d('Toggled selection for message $messageId in chat $chatId');
  }

  /// Select all messages in a chat
  void selectAll(String chatId, List<String> messageIds) {
    state = {...state, chatId: Set<String>.from(messageIds)};
    _logger.d('Selected all ${messageIds.length} messages in chat $chatId');
  }

  /// Clear selection for a chat
  void clearSelection(String chatId) {
    final newState = Map<String, Set<String>>.from(state);
    newState.remove(chatId);
    state = newState;
    _logger.d('Cleared selection for chat $chatId');
  }

  /// Get selected messages for a chat
  Set<String> getSelectedMessages(String chatId) {
    return state[chatId] ?? <String>{};
  }

  /// Check if a message is selected
  bool isSelected(String chatId, String messageId) {
    return state[chatId]?.contains(messageId) ?? false;
  }
}

/// Provider for message selection
final messageSelectionProvider =
    StateNotifierProvider<MessageSelectionNotifier, Map<String, Set<String>>>((
      ref,
    ) {
      return MessageSelectionNotifier();
    });

/// Provider to get selected messages for a specific chat
final chatSelectedMessagesProvider = Provider.family<Set<String>, String>((
  ref,
  chatId,
) {
  final selectionState = ref.watch(messageSelectionProvider);
  return selectionState[chatId] ?? <String>{};
});

/// Provider for message bulk operations
final messageBulkOperationsProvider =
    Provider.family<MessageBulkOperations, String>((ref, chatId) {
      return MessageBulkOperations(ref, chatId);
    });

/// Class for handling bulk message operations
class MessageBulkOperations {
  MessageBulkOperations(this._ref, this._chatId);

  final Ref _ref;
  final String _chatId;

  /// Delete selected messages
  Future<void> deleteSelectedMessages() async {
    final selectedMessages = _ref.read(chatSelectedMessagesProvider(_chatId));
    if (selectedMessages.isEmpty) return;

    final messageMutation = _ref.read(messageMutationProvider.notifier);

    _logger.i(
      'Deleting ${selectedMessages.length} selected messages in chat $_chatId',
    );

    for (final messageId in selectedMessages) {
      try {
        await messageMutation.deleteMessage(messageId, _chatId);
      } catch (e) {
        _logger.e('Error deleting message $messageId: $e');
      }
    }

    // Clear selection after deletion
    _ref.read(messageSelectionProvider.notifier).clearSelection(_chatId);
    _logger.i('Successfully deleted selected messages in chat $_chatId');
  }

  /// Mark selected messages as read
  Future<void> markSelectedAsRead() async {
    final selectedMessages = _ref.read(chatSelectedMessagesProvider(_chatId));
    if (selectedMessages.isEmpty) return;

    final messageMutation = _ref.read(messageMutationProvider.notifier);

    _logger.i(
      'Marking ${selectedMessages.length} selected messages as read in chat $_chatId',
    );

    for (final messageId in selectedMessages) {
      try {
        await messageMutation.updateMessageStatus(
          messageId,
          _chatId,
          MessageStatus.read,
        );
      } catch (e) {
        _logger.e('Error marking message $messageId as read: $e');
      }
    }

    _logger.i('Successfully marked selected messages as read in chat $_chatId');
  }
}

/// Provider for message search functionality
final messageSearchProvider =
    StateNotifierProvider.family<
      MessageSearchNotifier,
      AsyncValue<List<MessageModel>>,
      String
    >((ref, chatId) {
      return MessageSearchNotifier(ref, chatId);
    });

/// State notifier for message search
class MessageSearchNotifier
    extends StateNotifier<AsyncValue<List<MessageModel>>> {
  MessageSearchNotifier(this._ref, this._chatId)
    : super(const AsyncValue.data([]));

  final Ref _ref;
  final String _chatId;

  /// Search messages in the chat
  Future<void> searchMessages(String query) async {
    if (query.trim().isEmpty) {
      state = const AsyncValue.data([]);
      return;
    }

    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      _logger.i('Searching messages in chat $_chatId for: "$query"');

      final searchResults = await _ref.read(
        searchMessagesProvider({
          'chatId': _chatId,
          'searchQuery': query.trim(),
        }).future,
      );

      _logger.i('Found ${searchResults.length} messages matching "$query"');
      return searchResults;
    });
  }

  /// Clear search results
  void clearSearch() {
    state = const AsyncValue.data([]);
    _logger.d('Cleared search results for chat $_chatId');
  }
}
