import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../models/chat_models.dart';
import 'chat_repository_providers.dart';
import 'chat_controller.dart';
import 'message_controller.dart';
import 'message_status_controller.dart';

/// Logger for chat real-time controller
final _logger = Logger();

/// State notifier for managing real-time chat updates
class ChatRealtimeNotifier extends StateNotifier<AsyncValue<List<ChatModel>>> {
  ChatRealtimeNotifier(this._ref) : super(const AsyncValue.loading()) {
    _initialize();
  }

  final Ref _ref;
  StreamSubscription<List<ChatModel>>? _chatsSubscription;

  /// Initialize real-time chat stream
  void _initialize() {
    final userId = _ref.read(currentUserIdProvider);
    final repository = _ref.read(chatRepositoryProvider);

    _logger.i('Initializing real-time chat stream for user: $userId');

    try {
      _chatsSubscription = repository
          .getChatsStreamForUser(userId)
          .listen(
            (chats) {
              _logger.d('Real-time chat update: ${chats.length} chats');
              state = AsyncValue.data(chats);
            },
            onError: (error, stackTrace) {
              _logger.e('Error in chat stream: $error');
              state = AsyncValue.error(error, stackTrace);
            },
          );
    } catch (e, stackTrace) {
      _logger.e('Error setting up chat stream: $e');
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Refresh the stream
  void refresh() {
    _logger.i('Refreshing real-time chat stream');
    _chatsSubscription?.cancel();
    state = const AsyncValue.loading();
    _initialize();
  }

  @override
  void dispose() {
    _logger.i('Disposing real-time chat stream');
    _chatsSubscription?.cancel();
    super.dispose();
  }
}

/// Provider for real-time chat updates
final chatRealtimeProvider =
    StateNotifierProvider<ChatRealtimeNotifier, AsyncValue<List<ChatModel>>>((
      ref,
    ) {
      return ChatRealtimeNotifier(ref);
    });

/// State notifier for managing real-time chat by ID
class ChatByIdRealtimeNotifier extends StateNotifier<AsyncValue<ChatModel?>> {
  ChatByIdRealtimeNotifier(this._ref, this._chatId)
    : super(const AsyncValue.loading()) {
    _initialize();
  }

  final Ref _ref;
  final String _chatId;
  StreamSubscription<ChatModel?>? _chatSubscription;

  /// Initialize real-time chat stream
  void _initialize() {
    final repository = _ref.read(chatRepositoryProvider);

    _logger.i('Initializing real-time stream for chat: $_chatId');

    try {
      _chatSubscription = repository
          .getChatStreamById(_chatId)
          .listen(
            (chat) {
              _logger.d('Real-time update for chat: $_chatId');
              state = AsyncValue.data(chat);
            },
            onError: (error, stackTrace) {
              _logger.e('Error in chat stream for $_chatId: $error');
              state = AsyncValue.error(error, stackTrace);
            },
          );
    } catch (e, stackTrace) {
      _logger.e('Error setting up chat stream for $_chatId: $e');
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Refresh the stream
  void refresh() {
    _logger.i('Refreshing real-time stream for chat: $_chatId');
    _chatSubscription?.cancel();
    state = const AsyncValue.loading();
    _initialize();
  }

  @override
  void dispose() {
    _logger.i('Disposing real-time stream for chat: $_chatId');
    _chatSubscription?.cancel();
    super.dispose();
  }
}

/// Provider for real-time chat by ID
final chatByIdRealtimeProvider =
    StateNotifierProvider.family<
      ChatByIdRealtimeNotifier,
      AsyncValue<ChatModel?>,
      String
    >((ref, chatId) {
      return ChatByIdRealtimeNotifier(ref, chatId);
    });

/// State notifier for managing real-time message updates
class MessagesRealtimeNotifier
    extends StateNotifier<AsyncValue<List<MessageModel>>> {
  MessagesRealtimeNotifier(this._ref, this._chatId)
    : super(const AsyncValue.loading()) {
    _initialize();
  }

  final Ref _ref;
  final String _chatId;
  StreamSubscription<List<MessageModel>>? _messagesSubscription;

  /// Initialize real-time messages stream
  void _initialize() {
    final repository = _ref.read(messageRepositoryProvider);

    _logger.i('Initializing real-time messages stream for chat: $_chatId');

    try {
      _messagesSubscription = repository
          .getMessagesStreamForChat(_chatId)
          .listen(
            (messages) {
              _logger.d(
                'Real-time messages update for chat $_chatId: ${messages.length} messages',
              );
              state = AsyncValue.data(messages);
            },
            onError: (error, stackTrace) {
              _logger.e('Error in messages stream for $_chatId: $error');
              state = AsyncValue.error(error, stackTrace);
            },
          );
    } catch (e, stackTrace) {
      _logger.e('Error setting up messages stream for $_chatId: $e');
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Refresh the stream
  void refresh() {
    _logger.i('Refreshing real-time messages stream for chat: $_chatId');
    _messagesSubscription?.cancel();
    state = const AsyncValue.loading();
    _initialize();
  }

  @override
  void dispose() {
    _logger.i('Disposing real-time messages stream for chat: $_chatId');
    _messagesSubscription?.cancel();
    super.dispose();
  }
}

/// Provider for real-time messages
final messagesRealtimeProvider =
    StateNotifierProvider.family<
      MessagesRealtimeNotifier,
      AsyncValue<List<MessageModel>>,
      String
    >((ref, chatId) {
      return MessagesRealtimeNotifier(ref, chatId);
    });

/// State notifier for managing connection status
class ConnectionStatusNotifier extends StateNotifier<ConnectionStatus> {
  ConnectionStatusNotifier() : super(ConnectionStatus.connected) {
    _startConnectionMonitoring();
  }

  Timer? _connectionTimer;

  /// Start monitoring connection status
  void _startConnectionMonitoring() {
    _connectionTimer = Timer.periodic(const Duration(seconds: 10), (_) {
      _checkConnection();
    });
  }

  /// Check connection status
  void _checkConnection() {
    // In a real app, you would check actual network connectivity
    // For now, we'll simulate connection status
    _logger.d('Checking connection status');

    // Simulate occasional disconnections for testing
    if (DateTime.now().millisecondsSinceEpoch % 100 == 0) {
      state = ConnectionStatus.disconnected;
      _logger.w('Connection lost');

      // Simulate reconnection after a delay
      Timer(const Duration(seconds: 3), () {
        state = ConnectionStatus.connected;
        _logger.i('Connection restored');
      });
    }
  }

  /// Manually set connection status
  void setConnectionStatus(ConnectionStatus status) {
    if (state != status) {
      state = status;
      _logger.i('Connection status changed to: ${status.name}');
    }
  }

  @override
  void dispose() {
    _connectionTimer?.cancel();
    super.dispose();
  }
}

/// Enum for connection status
enum ConnectionStatus { connected, connecting, disconnected, reconnecting }

/// Provider for connection status
final connectionStatusProvider =
    StateNotifierProvider<ConnectionStatusNotifier, ConnectionStatus>((ref) {
      return ConnectionStatusNotifier();
    });

/// State notifier for managing offline message queue
class OfflineMessageQueueNotifier extends StateNotifier<List<MessageModel>> {
  OfflineMessageQueueNotifier(this._ref) : super([]);

  final Ref _ref;

  /// Add message to offline queue
  void addToQueue(MessageModel message) {
    state = [...state, message];
    _logger.i('Added message to offline queue: ${message.id}');
  }

  /// Remove message from queue
  void removeFromQueue(String messageId) {
    state = state.where((message) => message.id != messageId).toList();
    _logger.i('Removed message from offline queue: $messageId');
  }

  /// Process offline queue when connection is restored
  Future<void> processQueue() async {
    if (state.isEmpty) return;

    _logger.i('Processing offline message queue: ${state.length} messages');

    final messageMutation = _ref.read(messageMutationProvider.notifier);
    final queuedMessages = List<MessageModel>.from(state);

    for (final message in queuedMessages) {
      try {
        await messageMutation.sendMessage(message);
        removeFromQueue(message.id);
        _logger.i('Successfully sent queued message: ${message.id}');
      } catch (e) {
        _logger.e('Error sending queued message ${message.id}: $e');
        // Keep message in queue for retry
      }
    }

    _logger.i('Finished processing offline queue');
  }

  /// Clear all queued messages
  void clearQueue() {
    state = [];
    _logger.i('Cleared offline message queue');
  }
}

/// Provider for offline message queue
final offlineMessageQueueProvider =
    StateNotifierProvider<OfflineMessageQueueNotifier, List<MessageModel>>((
      ref,
    ) {
      return OfflineMessageQueueNotifier(ref);
    });

/// Provider for automatic offline queue processing
final offlineQueueProcessorProvider = Provider<OfflineQueueProcessor>((ref) {
  return OfflineQueueProcessor(ref);
});

/// Class for handling automatic offline queue processing
class OfflineQueueProcessor {
  OfflineQueueProcessor(this._ref) {
    _startMonitoring();
  }

  final Ref _ref;
  ProviderSubscription<ConnectionStatus>? _connectionSubscription;

  /// Start monitoring connection status for queue processing
  void _startMonitoring() {
    _connectionSubscription = _ref.listen(connectionStatusProvider, (
      previous,
      next,
    ) {
      if (previous == ConnectionStatus.disconnected &&
          next == ConnectionStatus.connected) {
        _logger.i('Connection restored, processing offline queue');
        _processOfflineQueue();
      }
    });
  }

  /// Process offline queue
  Future<void> _processOfflineQueue() async {
    try {
      final queueNotifier = _ref.read(offlineMessageQueueProvider.notifier);
      await queueNotifier.processQueue();
    } catch (e) {
      _logger.e('Error processing offline queue: $e');
    }
  }

  void dispose() {
    _connectionSubscription?.close();
  }
}

/// Provider for real-time synchronization manager
final realtimeSyncManagerProvider = Provider<RealtimeSyncManager>((ref) {
  return RealtimeSyncManager(ref);
});

/// Class for managing overall real-time synchronization
class RealtimeSyncManager {
  RealtimeSyncManager(this._ref);

  final Ref _ref;

  /// Start real-time synchronization for a chat
  void startChatSync(String chatId) {
    _logger.i('Starting real-time sync for chat: $chatId');

    // Initialize real-time providers
    _ref.read(chatByIdRealtimeProvider(chatId));
    _ref.read(messagesRealtimeProvider(chatId));

    // Start auto status updates
    final statusUpdater = _ref.read(messageStatusAutoUpdaterProvider(chatId));
    statusUpdater.startAutoUpdates();
  }

  /// Stop real-time synchronization for a chat
  void stopChatSync(String chatId) {
    _logger.i('Stopping real-time sync for chat: $chatId');

    // Stop auto status updates
    final statusUpdater = _ref.read(messageStatusAutoUpdaterProvider(chatId));
    statusUpdater.stopAutoUpdates();

    // Note: Providers will be disposed automatically when no longer watched
  }

  /// Refresh all real-time streams
  void refreshAllStreams() {
    _logger.i('Refreshing all real-time streams');

    // Refresh chat list stream
    _ref.read(chatRealtimeProvider.notifier).refresh();

    // Note: Individual chat streams would need to be refreshed separately
    // This would typically be done when the user navigates to specific chats
  }

  /// Handle connection status changes
  void handleConnectionChange(ConnectionStatus status) {
    _logger.i('Handling connection change: ${status.name}');

    switch (status) {
      case ConnectionStatus.connected:
        // Refresh streams and process offline queue
        refreshAllStreams();
        break;
      case ConnectionStatus.disconnected:
        // Streams will automatically handle disconnection
        _logger.w('Connection lost, streams will retry automatically');
        break;
      case ConnectionStatus.reconnecting:
        _logger.i('Reconnecting...');
        break;
      case ConnectionStatus.connecting:
        _logger.i('Connecting...');
        break;
    }
  }
}
