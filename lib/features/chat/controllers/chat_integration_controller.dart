import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../models/chat_models.dart';
import '../enums/chat_enums.dart';
import 'chat_controller.dart';
import 'message_controller.dart';
import 'chat_creation_controller.dart';

/// Logger for chat integration controller
final _logger = Logger();

/// State notifier for Digital Library integration
class DigitalLibraryIntegrationNotifier
    extends StateNotifier<AsyncValue<void>> {
  DigitalLibraryIntegrationNotifier(this._ref)
    : super(const AsyncValue.data(null));

  final Ref _ref;

  /// Send file from Digital Library as message
  Future<MessageModel> sendFileMessage(
    String chatId,
    String fileId,
    String fileName,
    String fileUrl,
    int fileSize,
  ) async {
    state = const AsyncValue.loading();

    return await AsyncValue.guard(() async {
      final userId = _ref.read(currentUserIdProvider);
      final messageMutation = _ref.read(messageMutationProvider.notifier);

      _logger.i('Sending file message: $fileName to chat: $chatId');

      // Create file attachment
      final attachment = MessageAttachment(
        id: fileId,
        fileName: fileName,
        fileSize: fileSize,
        mimeType: _getMimeType(fileName),
        fileUrl: fileUrl,
        uploadedAt: DateTime.now(),
        isFromLibrary: true,
        libraryFileId: fileId,
      );

      // Create message with attachment
      final message = MessageModel(
        id: '', // Will be set by repository
        chatId: chatId,
        senderId: userId,
        senderName: 'Current User', // TODO: Get from user profile
        type: MessageType.file,
        content: 'Shared a file: $fileName',
        attachments: [attachment],
        reactions: {},
        status: MessageStatus.sending,
        sentAt: DateTime.now(),
        isDeleted: false,
      );

      final sentMessage = await messageMutation.sendMessage(message);

      _logger.i('Successfully sent file message: ${sentMessage.id}');
      return sentMessage;
    }).then((result) {
      state = result.when(
        data: (_) => const AsyncValue.data(null),
        loading: () => const AsyncValue.loading(),
        error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
      );

      return result.when(
        data: (message) => message,
        loading: () => throw Exception('Unexpected loading state'),
        error: (error, stackTrace) => throw error,
      );
    });
  }

  /// Send image from Digital Library as message
  Future<MessageModel> sendImageMessage(
    String chatId,
    String imageId,
    String imageName,
    String imageUrl,
    int imageSize,
  ) async {
    state = const AsyncValue.loading();

    return await AsyncValue.guard(() async {
      final userId = _ref.read(currentUserIdProvider);
      final messageMutation = _ref.read(messageMutationProvider.notifier);

      _logger.i('Sending image message: $imageName to chat: $chatId');

      // Create image attachment
      final attachment = MessageAttachment(
        id: imageId,
        fileName: imageName,
        fileSize: imageSize,
        mimeType: _getMimeType(imageName),
        fileUrl: imageUrl,
        uploadedAt: DateTime.now(),
        isFromLibrary: true,
        libraryFileId: imageId,
      );

      // Create message with attachment
      final message = MessageModel(
        id: '', // Will be set by repository
        chatId: chatId,
        senderId: userId,
        senderName: 'Current User', // TODO: Get from user profile
        type: MessageType.image,
        content: 'Shared an image: $imageName',
        attachments: [attachment],
        reactions: {},
        status: MessageStatus.sending,
        sentAt: DateTime.now(),
        isDeleted: false,
      );

      final sentMessage = await messageMutation.sendMessage(message);

      _logger.i('Successfully sent image message: ${sentMessage.id}');
      return sentMessage;
    }).then((result) {
      state = result.when(
        data: (_) => const AsyncValue.data(null),
        loading: () => const AsyncValue.loading(),
        error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
      );

      return result.when(
        data: (message) => message,
        loading: () => throw Exception('Unexpected loading state'),
        error: (error, stackTrace) => throw error,
      );
    });
  }

  /// Get MIME type from file name
  String _getMimeType(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();

    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      case 'mp3':
        return 'audio/mpeg';
      case 'wav':
        return 'audio/wav';
      case 'aac':
        return 'audio/aac';
      case 'm4a':
        return 'audio/mp4';
      case 'mp4':
        return 'video/mp4';
      case 'mov':
        return 'video/quicktime';
      case 'avi':
        return 'video/x-msvideo';
      case 'mkv':
        return 'video/x-matroska';
      case 'pdf':
        return 'application/pdf';
      case 'doc':
        return 'application/msword';
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case 'txt':
        return 'text/plain';
      default:
        return 'application/octet-stream';
    }
  }
}

/// Provider for Digital Library integration
final digitalLibraryIntegrationProvider =
    StateNotifierProvider<DigitalLibraryIntegrationNotifier, AsyncValue<void>>((
      ref,
    ) {
      return DigitalLibraryIntegrationNotifier(ref);
    });

/// Provider for user discovery integration
final userDiscoveryIntegrationProvider =
    FutureProvider.family<List<Map<String, dynamic>>, String>((
      ref,
      searchQuery,
    ) async {
      // In a real app, this would integrate with the user management system
      // For now, we'll return mock user data
      await Future.delayed(
        const Duration(milliseconds: 500),
      ); // Simulate network delay

      final mockUsers = [
        {
          'id': 'user_teacher_1',
          'name': 'Dr. Sarah Johnson',
          'email': '<EMAIL>',
          'type': 'teacher',
          'avatar': 'https://example.com/avatar1.jpg',
          'isOnline': true,
        },
        {
          'id': 'user_student_1',
          'name': 'Alex Chen',
          'email': '<EMAIL>',
          'type': 'student',
          'avatar': 'https://example.com/avatar2.jpg',
          'isOnline': false,
        },
        {
          'id': 'user_student_2',
          'name': 'Maria Rodriguez',
          'email': '<EMAIL>',
          'type': 'student',
          'avatar': 'https://example.com/avatar3.jpg',
          'isOnline': true,
        },
        {
          'id': 'user_parent_1',
          'name': 'John Smith',
          'email': '<EMAIL>',
          'type': 'parent',
          'avatar': 'https://example.com/avatar4.jpg',
          'isOnline': false,
        },
      ];

      if (searchQuery.isEmpty) {
        return mockUsers;
      }

      final filteredUsers = mockUsers
          .where(
            (user) =>
                user['name'].toString().toLowerCase().contains(
                  searchQuery.toLowerCase(),
                ) ||
                user['email'].toString().toLowerCase().contains(
                  searchQuery.toLowerCase(),
                ),
          )
          .toList();

      _logger.i('Found ${filteredUsers.length} users matching "$searchQuery"');
      return filteredUsers;
    });

/// Provider for classroom integration
final classroomIntegrationProvider =
    FutureProvider.family<List<Map<String, dynamic>>, String>((
      ref,
      classroomId,
    ) async {
      // In a real app, this would integrate with the classroom system
      // For now, we'll return mock classroom data
      await Future.delayed(
        const Duration(milliseconds: 300),
      ); // Simulate network delay

      final mockClassroomData = {
        'id': classroomId,
        'name': 'Advanced Mathematics',
        'teacher': {'id': 'teacher_1', 'name': 'Dr. Sarah Johnson'},
        'students': [
          {'id': 'student_1', 'name': 'Alex Chen'},
          {'id': 'student_2', 'name': 'Maria Rodriguez'},
          {'id': 'student_3', 'name': 'David Kim'},
        ],
        'announcements': [
          {
            'id': 'announcement_1',
            'title': 'Quiz Next Week',
            'content': 'We will have a quiz on Chapter 5 next Tuesday.',
            'createdAt': DateTime.now().subtract(const Duration(days: 2)),
          },
        ],
      };

      _logger.i('Fetched classroom data for: $classroomId');
      return [mockClassroomData];
    });

/// State notifier for navigation integration
class NavigationIntegrationNotifier extends StateNotifier<String?> {
  NavigationIntegrationNotifier() : super(null);

  /// Navigate to chat from external source
  void navigateToChat(String chatId, {String? source}) {
    state = chatId;
    _logger.i(
      'Navigating to chat: $chatId from source: ${source ?? 'unknown'}',
    );
  }

  /// Navigate to chat creation
  void navigateToChatCreation({ChatType? type, String? classroomId}) {
    _logger.i(
      'Navigating to chat creation with type: ${type?.value} and classroom: $classroomId',
    );
    // In a real app, this would trigger navigation
  }

  /// Clear navigation state
  void clearNavigation() {
    state = null;
  }
}

/// Provider for navigation integration
final navigationIntegrationProvider =
    StateNotifierProvider<NavigationIntegrationNotifier, String?>((ref) {
      return NavigationIntegrationNotifier();
    });

/// Provider for dashboard integration
final dashboardIntegrationProvider = FutureProvider<Map<String, dynamic>>((
  ref,
) async {
  final userChats = await ref.watch(userChatsProvider.future);
  final unreadCount = await ref.watch(unreadMessageCountProvider.future);

  // Get recent chats for dashboard
  final recentChats = userChats
      .take(3)
      .map(
        (chat) => {
          'id': chat.id,
          'title': chat.title,
          'type': chat.type.value,
          'lastMessage': chat.lastMessage?.content ?? 'No messages yet',
          'lastMessageAt': chat.lastMessageAt?.toIso8601String(),
          'unreadCount': chat.unreadCount,
        },
      )
      .toList();

  final dashboardData = {
    'totalChats': userChats.length,
    'unreadMessages': unreadCount,
    'recentChats': recentChats,
    'activeChats': userChats.where((chat) => chat.isActive).length,
  };

  _logger.i(
    'Prepared dashboard data: ${dashboardData['totalChats']} chats, ${dashboardData['unreadMessages']} unread',
  );
  return dashboardData;
});

/// Provider for homework integration
final homeworkIntegrationProvider =
    FutureProvider.family<List<Map<String, dynamic>>, String>((
      ref,
      homeworkId,
    ) async {
      // In a real app, this would integrate with the homework system
      // For now, we'll return mock homework-related chat data
      await Future.delayed(
        const Duration(milliseconds: 200),
      ); // Simulate network delay

      final mockHomeworkChats = [
        {
          'id': 'chat_homework_1',
          'title': 'Math Homework Discussion',
          'type': 'study_group',
          'homeworkId': homeworkId,
          'participants': ['student_1', 'student_2', 'student_3'],
          'createdAt': DateTime.now().subtract(const Duration(days: 1)),
        },
        {
          'id': 'chat_homework_2',
          'title': 'Help with Problem 5',
          'type': 'one_to_one',
          'homeworkId': homeworkId,
          'participants': ['student_1', 'teacher_1'],
          'createdAt': DateTime.now().subtract(const Duration(hours: 3)),
        },
      ];

      _logger.i(
        'Found ${mockHomeworkChats.length} homework-related chats for: $homeworkId',
      );
      return mockHomeworkChats;
    });

/// Provider for notification integration
final notificationIntegrationProvider = Provider<NotificationIntegration>((
  ref,
) {
  return NotificationIntegration(ref);
});

/// Class for handling notification integration
class NotificationIntegration {
  NotificationIntegration(this._ref);

  final Ref _ref;

  /// Handle incoming message notification
  void handleMessageNotification(Map<String, dynamic> notificationData) {
    final chatId = notificationData['chatId'] as String?;
    final messageId = notificationData['messageId'] as String?;
    final senderId = notificationData['senderId'] as String?;

    if (chatId != null && messageId != null) {
      _logger.i(
        'Handling message notification for chat: $chatId, message: $messageId from: $senderId',
      );

      // Navigate to chat
      final navigation = _ref.read(navigationIntegrationProvider.notifier);
      navigation.navigateToChat(chatId, source: 'notification');

      // Mark as read if needed
      // This would be handled by the UI when the chat is opened
    }
  }

  /// Handle chat invitation notification
  void handleChatInvitationNotification(Map<String, dynamic> notificationData) {
    final chatId = notificationData['chatId'] as String?;
    final inviterId = notificationData['inviterId'] as String?;

    if (chatId != null) {
      _logger.i(
        'Handling chat invitation notification for chat: $chatId from: $inviterId',
      );

      // Navigate to chat
      final navigation = _ref.read(navigationIntegrationProvider.notifier);
      navigation.navigateToChat(chatId, source: 'invitation');
    }
  }

  /// Send notification for new message
  void sendMessageNotification(String chatId, MessageModel message) {
    _logger.i('Sending notification for new message in chat: $chatId');

    // In a real app, this would integrate with the notification system
    // to send push notifications to other participants
  }
}

/// Provider for feature integration utilities
final featureIntegrationUtilsProvider = Provider<FeatureIntegrationUtils>((
  ref,
) {
  return FeatureIntegrationUtils(ref);
});

/// Utility class for feature integration operations
class FeatureIntegrationUtils {
  FeatureIntegrationUtils(this._ref);

  final Ref _ref;

  /// Create chat from classroom context
  Future<ChatModel> createChatFromClassroom(
    String classroomId,
    String title,
    List<String> participantIds,
  ) async {
    final chatCreation = _ref.read(chatCreationProvider.notifier);

    return await chatCreation.createClassroomDiscussion(
      title,
      classroomId,
      participantIds,
      description: 'Created from classroom context',
    );
  }

  /// Create chat from homework context
  Future<ChatModel> createChatFromHomework(
    String homeworkId,
    String title,
    List<String> participantIds,
  ) async {
    final chatCreation = _ref.read(chatCreationProvider.notifier);

    return await chatCreation.createStudyGroup(
      title,
      participantIds,
      description: 'Created for homework discussion: $homeworkId',
    );
  }

  /// Get chat suggestions based on context
  Future<List<Map<String, dynamic>>> getChatSuggestions(String context) async {
    switch (context) {
      case 'dashboard':
        return [
          {
            'type': 'recent_contacts',
            'title': 'Message Recent Contacts',
            'description':
                'Start a conversation with someone you\'ve talked to recently',
          },
          {
            'type': 'study_group',
            'title': 'Create Study Group',
            'description': 'Form a group to collaborate on assignments',
          },
        ];
      case 'classroom':
        return [
          {
            'type': 'classroom_discussion',
            'title': 'Start Class Discussion',
            'description': 'Begin a discussion about class topics',
          },
          {
            'type': 'ask_teacher',
            'title': 'Ask Teacher',
            'description': 'Send a direct message to your teacher',
          },
        ];
      case 'homework':
        return [
          {
            'type': 'homework_help',
            'title': 'Get Help',
            'description':
                'Ask classmates or teacher for help with this assignment',
          },
          {
            'type': 'study_group',
            'title': 'Form Study Group',
            'description': 'Create a group to work on this assignment together',
          },
        ];
      default:
        return [];
    }
  }

  /// Check if chat feature is available in context
  bool isChatAvailableInContext(String context) {
    // In a real app, this would check feature flags and permissions
    return true;
  }
}
