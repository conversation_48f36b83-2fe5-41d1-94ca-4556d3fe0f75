import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../repositories/chat_repositories.dart';

/// Provider for ChatRepository instance
final chatRepositoryProvider = Provider<ChatRepository>((ref) {
  return ChatRepository();
});

/// Provider for MessageRepository instance
final messageRepositoryProvider = Provider<MessageRepository>((ref) {
  return MessageRepository();
});

/// Provider for ParticipantRepository instance
final participantRepositoryProvider = Provider<ParticipantRepository>((ref) {
  return ParticipantRepository();
});
