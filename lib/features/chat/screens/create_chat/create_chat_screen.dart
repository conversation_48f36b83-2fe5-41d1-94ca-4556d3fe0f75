import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:go_router/go_router.dart';
import 'package:logger/logger.dart';

import '../../enums/chat_type.dart';
import '../../models/chat_model.dart';
import '../../controllers/chat_creation_controller.dart';
import '../../../../core/routes/app_routes.dart';
import 'sections/chat_type_selection_section.dart';
import 'sections/participant_selection_section.dart';
import 'sections/chat_details_section.dart';

/// Screen for creating a new chat
class CreateChatScreen extends ConsumerStatefulWidget {
  const CreateChatScreen({
    super.key,
    this.initialChatType,
    this.initialStep,
    this.selectionMode,
  });

  final ChatType? initialChatType;
  final String? initialStep;
  final String?
  selectionMode; // 'single' for direct message, 'multiple' for group

  @override
  ConsumerState<CreateChatScreen> createState() => _CreateChatScreenState();
}

class _CreateChatScreenState extends ConsumerState<CreateChatScreen> {
  final PageController _pageController = PageController();
  final Logger _logger = Logger();
  int _currentStep = 0;

  ChatType? _selectedChatType;
  List<String> _selectedParticipants = [];
  String _chatTitle = '';
  String _chatDescription = '';
  bool _isCreating = false;

  @override
  void initState() {
    super.initState();
    _initializeFromParameters();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _initializeFromParameters() {
    if (widget.initialChatType != null) {
      _selectedChatType = widget.initialChatType;
    }

    if (widget.initialStep == 'participants' && _selectedChatType != null) {
      // When coming from modal, we start at participant selection (page 0)
      // No need to animate to a different page since we're already starting there
      _currentStep = 0;
    }
  }

  void _onChatTypeSelected(ChatType chatType) {
    setState(() {
      _selectedChatType = chatType;
    });
    _nextStep();
  }

  void _onParticipantsSelected(List<String> participants) {
    setState(() {
      _selectedParticipants = participants;
    });

    // Handle different flows based on selection mode
    if (widget.selectionMode == 'single' &&
        _selectedChatType == ChatType.oneToOne) {
      // For direct messages, create chat immediately after selecting participant
      _createDirectMessage();
    } else {
      // For group chats, proceed to next step (settings)
      _nextStep();
    }
  }

  void _onChatDetailsCompleted(String title, String description) {
    setState(() {
      _chatTitle = title;
      _chatDescription = description;
    });
    _onCreateChat();
  }

  void _nextStep() {
    final maxSteps = _getMaxSteps();
    if (_currentStep < maxSteps - 1) {
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  int _getMaxSteps() {
    // If we have initial parameters (coming from modal)
    if (widget.initialChatType != null &&
        widget.initialStep == 'participants') {
      if (widget.selectionMode == 'multiple') {
        return 2; // Participant selection + Chat details
      } else {
        return 1; // Just participant selection for direct messages
      }
    }
    // Default full flow
    return 3; // Chat type + Participant selection + Chat details
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _createDirectMessage() async {
    if (_selectedParticipants.isEmpty || _isCreating) return;

    setState(() {
      _isCreating = true;
    });

    try {
      final chatCreation = ref.read(chatCreationProvider.notifier);
      final otherUserId = _selectedParticipants.first;

      _logger.i('Creating direct message with user: $otherUserId');

      // Check if chat already exists
      final utils = ref.read(chatCreationUtilsProvider);
      final existingChat = await utils.findExistingChat(ChatType.oneToOne, [
        otherUserId,
      ]);

      if (existingChat != null) {
        _logger.i('Found existing chat: ${existingChat.id}');
        if (mounted) {
          context.pushReplacementNamed(
            RouteNames.chatDetail,
            pathParameters: {'id': existingChat.id},
          );
        }
        return;
      }

      final createdChat = await chatCreation.createOneToOneChat(otherUserId);

      _logger.i('Successfully created direct message: ${createdChat.id}');

      if (mounted) {
        context.pushReplacementNamed(
          RouteNames.chatDetail,
          pathParameters: {'id': createdChat.id},
        );
      }
    } catch (e, stackTrace) {
      _logger.e(
        'Error creating direct message: $e',
        error: e,
        stackTrace: stackTrace,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create chat: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _createDirectMessage,
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isCreating = false;
        });
      }
    }
  }

  void _onCreateChat() async {
    if (_selectedChatType == null ||
        _selectedParticipants.isEmpty ||
        _isCreating) {
      return;
    }

    setState(() {
      _isCreating = true;
    });

    try {
      final chatCreation = ref.read(chatCreationProvider.notifier);
      final utils = ref.read(chatCreationUtilsProvider);

      // Generate title if not provided
      final title = _chatTitle.isNotEmpty
          ? _chatTitle
          : utils.getRecommendedTitle(
              _selectedChatType!,
              _selectedParticipants,
            );

      _logger.i('Creating ${_selectedChatType!.value} chat: $title');

      late final ChatModel createdChat;

      switch (_selectedChatType!) {
        case ChatType.groupChat:
          createdChat = await chatCreation.createGroupChat(
            title,
            _selectedParticipants,
            description: _chatDescription.isNotEmpty ? _chatDescription : null,
          );
          break;
        case ChatType.studyGroup:
          createdChat = await chatCreation.createStudyGroup(
            title,
            _selectedParticipants,
            description: _chatDescription.isNotEmpty ? _chatDescription : null,
          );
          break;
        case ChatType.oneToOne:
          createdChat = await chatCreation.createOneToOneChat(
            _selectedParticipants.first,
            title: title,
          );
          break;
        default:
          throw Exception('Unsupported chat type: ${_selectedChatType!.value}');
      }

      _logger.i('Successfully created chat: ${createdChat.id}');

      if (mounted) {
        context.pushReplacementNamed(
          RouteNames.chatDetail,
          pathParameters: {'id': createdChat.id},
        );
      }
    } catch (e, stackTrace) {
      _logger.e('Error creating chat: $e', error: e, stackTrace: stackTrace);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create chat: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
            action: SnackBarAction(label: 'Retry', onPressed: _onCreateChat),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isCreating = false;
        });
      }
    }
  }

  List<Widget> _buildPageViewChildren() {
    // If we have initial parameters (coming from modal), skip chat type selection
    if (widget.initialChatType != null &&
        widget.initialStep == 'participants') {
      return [
        // Step 1: Participant selection (skipping chat type selection)
        ParticipantSelectionSection(
          chatType: _selectedChatType!,
          onParticipantsSelected: _onParticipantsSelected,
          onBack: () =>
              context.pop(), // Go back to chat list instead of previous step
          selectionMode: widget.selectionMode,
        ),

        // Step 2: Chat details (only for group chats)
        if (widget.selectionMode == 'multiple')
          ChatDetailsSection(
            chatType: _selectedChatType!,
            participants: _selectedParticipants,
            onDetailsCompleted: _onChatDetailsCompleted,
            onBack: _previousStep,
          ),
      ];
    }

    // Default full flow (when accessed directly)
    return [
      // Step 1: Chat type selection
      ChatTypeSelectionSection(onChatTypeSelected: _onChatTypeSelected),

      // Step 2: Participant selection
      ParticipantSelectionSection(
        chatType: _selectedChatType!,
        onParticipantsSelected: _onParticipantsSelected,
        onBack: _previousStep,
        selectionMode: widget.selectionMode,
      ),

      // Step 3: Chat details
      ChatDetailsSection(
        chatType: _selectedChatType!,
        participants: _selectedParticipants,
        onDetailsCompleted: _onChatDetailsCompleted,
        onBack: _previousStep,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Create Chat',
          style: theme.textTheme.titleLarge?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: colorScheme.surface,
        elevation: 0,
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: Icon(Symbols.arrow_back_ios, color: colorScheme.onSurface),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Page view
            Expanded(
              child: PageView(
                controller: _pageController,
                physics: const NeverScrollableScrollPhysics(),
                children: _buildPageViewChildren(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
