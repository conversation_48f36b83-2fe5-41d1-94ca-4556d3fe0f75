import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:go_router/go_router.dart';

import 'sections/notification_settings_section.dart';
import 'sections/privacy_settings_section.dart';
import 'sections/chat_info_section.dart';
import 'sections/danger_zone_section.dart';

/// Screen for chat settings and preferences
class ChatSettingsScreen extends ConsumerStatefulWidget {
  /// The ID of the chat
  final String chatId;

  const ChatSettingsScreen({super.key, required this.chatId});

  @override
  ConsumerState<ChatSettingsScreen> createState() => _ChatSettingsScreenState();
}

class _ChatSettingsScreenState extends ConsumerState<ChatSettingsScreen> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Chat Settings',
          style: theme.textTheme.titleLarge?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: colorScheme.surface,
        elevation: 0,
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: Icon(Symbols.arrow_back_ios, color: colorScheme.onSurface),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Chat info section
              ChatInfoSection(chatId: widget.chatId),

              SizedBox(height: 24.h),

              // Notification settings
              NotificationSettingsSection(chatId: widget.chatId),

              SizedBox(height: 24.h),

              // Privacy settings
              PrivacySettingsSection(chatId: widget.chatId),

              SizedBox(height: 24.h),

              // Danger zone
              DangerZoneSection(chatId: widget.chatId),

              SizedBox(height: 32.h),
            ],
          ),
        ),
      ),
    );
  }
}
