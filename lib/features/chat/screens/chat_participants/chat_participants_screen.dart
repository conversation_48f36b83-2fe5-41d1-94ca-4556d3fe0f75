import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:go_router/go_router.dart';

import 'sections/participants_list_section.dart';
import 'sections/add_participants_section.dart';
import 'sections/participant_actions_section.dart';

/// Screen for managing chat participants
class ChatParticipantsScreen extends ConsumerStatefulWidget {
  /// The ID of the chat
  final String chatId;

  const ChatParticipantsScreen({super.key, required this.chatId});

  @override
  ConsumerState<ChatParticipantsScreen> createState() =>
      _ChatParticipantsScreenState();
}

class _ChatParticipantsScreenState
    extends ConsumerState<ChatParticipantsScreen> {
  bool _isAddingParticipants = false;

  void _onAddParticipants() {
    setState(() {
      _isAddingParticipants = true;
    });
  }

  void _onCancelAddParticipants() {
    setState(() {
      _isAddingParticipants = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Participants',
          style: theme.textTheme.titleLarge?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: colorScheme.surface,
        elevation: 0,
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: Icon(Symbols.arrow_back_ios, color: colorScheme.onSurface),
        ),
        actions: [
          if (!_isAddingParticipants)
            IconButton(
              onPressed: _onAddParticipants,
              icon: Icon(Symbols.person_add, color: colorScheme.onSurface),
            ),
        ],
      ),
      body: SafeArea(
        child: _isAddingParticipants
            ? AddParticipantsSection(
                chatId: widget.chatId,
                onCancel: _onCancelAddParticipants,
              )
            : Column(
                children: [
                  // Participants list
                  Expanded(
                    child: ParticipantsListSection(chatId: widget.chatId),
                  ),

                  // Participant actions
                  ParticipantActionsSection(chatId: widget.chatId),
                ],
              ),
      ),
    );
  }
}
