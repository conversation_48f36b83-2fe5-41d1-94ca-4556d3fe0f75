import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:go_router/go_router.dart';
import 'package:file_picker/file_picker.dart';
import 'package:logger/logger.dart';

import '../../../../core/routes/app_routes.dart';
import '../../providers/optimized_chat_providers.dart';
import 'sections/chat_header_section.dart';
import 'sections/messages_list_section.dart';
import 'sections/message_input_section.dart';
import 'sections/loading_state_section.dart';

/// Chat detail screen showing conversation messages and input
class ChatDetailScreen extends ConsumerStatefulWidget {
  /// The ID of the chat to display
  final String chatId;

  const ChatDetailScreen({super.key, required this.chatId});

  @override
  ConsumerState<ChatDetailScreen> createState() => _ChatDetailScreenState();
}

class _ChatDetailScreenState extends ConsumerState<ChatDetailScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final Logger _logger = Logger();
  final bool _isLoading = false;
  bool _isSending = false;

  @override
  void initState() {
    super.initState();
    _messageController.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _messageController.removeListener(_onTextChanged);
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final currentUserId = ref.read(currentUserIdProvider);
    final chatOperations = ref.read(chatOperationsProvider.notifier);

    if (_messageController.text.trim().isNotEmpty) {
      // Start typing indicator
      chatOperations.startTyping(
        widget.chatId,
        currentUserId,
        'Current User', // TODO: Get from user profile
      );
    } else {
      // Stop typing indicator
      chatOperations.stopTyping(widget.chatId, currentUserId);
    }
  }

  void _onSendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty || _isSending) return;

    setState(() {
      _isSending = true;
    });

    try {
      final currentUserId = ref.read(currentUserIdProvider);
      final chatOperations = ref.read(chatOperationsProvider.notifier);

      _logger.i('Sending message to chat: ${widget.chatId}');

      // Clear the input immediately for better UX
      _messageController.clear();

      // Stop typing indicator
      await chatOperations.stopTyping(widget.chatId, currentUserId);

      // Send the message
      await chatOperations.sendMessage(
        chatId: widget.chatId,
        content: message,
        senderId: currentUserId,
        senderName: 'Current User', // TODO: Get from user profile
      );

      _logger.i('Successfully sent message to chat: ${widget.chatId}');

      // Scroll to bottom after sending
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });
    } catch (e, stackTrace) {
      _logger.e('Error sending message: $e', error: e, stackTrace: stackTrace);

      // Show error to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to send message: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
            action: SnackBarAction(
              label: 'Retry',
              onPressed: () {
                _messageController.text = message;
                _onSendMessage();
              },
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSending = false;
        });
      }
    }
  }

  void _onAttachFile() async {
    try {
      _logger.i('Opening file picker for chat: ${widget.chatId}');

      final result = await FilePicker.platform.pickFiles(
        type: FileType.any,
        allowMultiple: false,
        withData: true,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        _logger.i('Selected file: ${file.name} (${file.size} bytes)');

        // For now, just show a placeholder message
        // TODO: Implement actual file upload and message sending
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('File selected: ${file.name}'),
              action: SnackBarAction(
                label: 'Send',
                onPressed: () {
                  // TODO: Implement file message sending
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('File upload not yet implemented'),
                    ),
                  );
                },
              ),
            ),
          );
        }
      } else {
        _logger.d('File picker cancelled');
      }
    } catch (e, stackTrace) {
      _logger.e('Error picking file: $e', error: e, stackTrace: stackTrace);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to pick file: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _onChatSettings() {
    context.pushNamed(
      RouteNames.chatSettings,
      pathParameters: {'id': widget.chatId},
    );
  }

  void _onViewParticipants() {
    context.pushNamed(
      RouteNames.chatParticipants,
      pathParameters: {'id': widget.chatId},
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: colorScheme.surface,
        elevation: 0,
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: Icon(Symbols.arrow_back_ios, color: colorScheme.onSurface),
        ),
        title: ChatHeaderSection(
          chatId: widget.chatId,
          onTap: _onViewParticipants,
        ),
        actions: [
          IconButton(
            onPressed: _onChatSettings,
            icon: Icon(Symbols.more_vert, color: colorScheme.onSurface),
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Messages list
            Expanded(
              child: _isLoading
                  ? const LoadingStateSection()
                  : MessagesListSection(
                      chatId: widget.chatId,
                      scrollController: _scrollController,
                    ),
            ),

            // Message input
            MessageInputSection(
              controller: _messageController,
              onSendMessage: _onSendMessage,
              onAttachFile: _onAttachFile,
              isLoading: _isSending,
            ),
          ],
        ),
      ),
    );
  }
}
