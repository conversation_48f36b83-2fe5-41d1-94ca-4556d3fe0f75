import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/routes/app_routes.dart';
import 'sections/chat_header_section.dart';
import 'sections/messages_list_section.dart';
import 'sections/message_input_section.dart';
import 'sections/loading_state_section.dart';

/// Chat detail screen showing conversation messages and input
class ChatDetailScreen extends ConsumerStatefulWidget {
  /// The ID of the chat to display
  final String chatId;

  const ChatDetailScreen({super.key, required this.chatId});

  @override
  ConsumerState<ChatDetailScreen> createState() => _ChatDetailScreenState();
}

class _ChatDetailScreenState extends ConsumerState<ChatDetailScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final bool _isLoading = false;

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onSendMessage() {
    final message = _messageController.text.trim();
    if (message.isEmpty) return;

    // TODO: Implement message sending logic
    _messageController.clear();

    // Scroll to bottom after sending
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _onAttachFile() {
    // TODO: Implement file attachment logic
  }

  void _onChatSettings() {
    context.pushNamed(
      RouteNames.chatSettings,
      pathParameters: {'id': widget.chatId},
    );
  }

  void _onViewParticipants() {
    context.pushNamed(
      RouteNames.chatParticipants,
      pathParameters: {'id': widget.chatId},
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: colorScheme.surface,
        elevation: 0,
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: Icon(Symbols.arrow_back_ios, color: colorScheme.onSurface),
        ),
        title: ChatHeaderSection(
          chatId: widget.chatId,
          onTap: _onViewParticipants,
        ),
        actions: [
          IconButton(
            onPressed: _onChatSettings,
            icon: Icon(Symbols.more_vert, color: colorScheme.onSurface),
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Messages list
            Expanded(
              child: _isLoading
                  ? const LoadingStateSection()
                  : MessagesListSection(
                      chatId: widget.chatId,
                      scrollController: _scrollController,
                    ),
            ),

            // Message input
            MessageInputSection(
              controller: _messageController,
              onSendMessage: _onSendMessage,
              onAttachFile: _onAttachFile,
            ),
          ],
        ),
      ),
    );
  }
}
