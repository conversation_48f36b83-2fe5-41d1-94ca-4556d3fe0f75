import 'package:cloud_firestore/cloud_firestore.dart';

/// Firebase configuration and optimization settings for chat functionality
/// This class centralizes all Firebase-specific configurations for better performance
class FirebaseChatConfig {
  // Private constructor to prevent instantiation
  FirebaseChatConfig._();

  /// Firestore instance with optimized settings
  static FirebaseFirestore get firestore {
    final instance = FirebaseFirestore.instance;
    
    // Enable offline persistence for better performance
    instance.settings = const Settings(
      persistenceEnabled: true,
      cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
    );
    
    return instance;
  }

  /// Message pagination configuration
  static const int messagesPageSize = 20;
  static const int maxCachedMessages = 100;
  static const int preloadMessagesCount = 5;

  /// Real-time listener configuration
  static const Duration presenceTimeout = Duration(minutes: 5);
  static const Duration typingTimeout = Duration(seconds: 3);
  static const Duration connectionRetryDelay = Duration(seconds: 2);
  static const int maxRetryAttempts = 3;

  /// Cache configuration
  static const Duration messageCacheDuration = Duration(hours: 24);
  static const Duration chatListCacheDuration = Duration(minutes: 30);
  static const Duration presenceCacheDuration = Duration(minutes: 1);

  /// Performance optimization settings
  static const int batchWriteSize = 500;
  static const Duration debounceDelay = Duration(milliseconds: 300);
  static const int maxConcurrentUploads = 3;

  /// Security and validation settings
  static const int maxMessageLength = 4000;
  static const int maxAttachmentSize = 25 * 1024 * 1024; // 25MB
  static const int maxAttachmentsPerMessage = 10;
  static const Duration rateLimitWindow = Duration(minutes: 1);
  static const int maxMessagesPerMinute = 30;

  /// Firestore indexes that should be created for optimal performance
  static const List<Map<String, dynamic>> requiredIndexes = [
    // Chat messages ordered by timestamp
    {
      'collection': 'chat_messages',
      'fields': [
        {'field': 'chatId', 'order': 'ASCENDING'},
        {'field': 'sentAt', 'order': 'DESCENDING'},
      ],
    },
    // Chat messages for pagination
    {
      'collection': 'chat_messages',
      'fields': [
        {'field': 'chatId', 'order': 'ASCENDING'},
        {'field': 'sentAt', 'order': 'ASCENDING'},
      ],
    },
    // User chats ordered by last message
    {
      'collection': 'chats',
      'fields': [
        {'field': 'participantIds', 'order': 'ASCENDING'},
        {'field': 'lastMessageAt', 'order': 'DESCENDING'},
      ],
    },
    // Active chats for user
    {
      'collection': 'chats',
      'fields': [
        {'field': 'participantIds', 'order': 'ASCENDING'},
        {'field': 'isActive', 'order': 'ASCENDING'},
        {'field': 'lastMessageAt', 'order': 'DESCENDING'},
      ],
    },
    // Message status tracking
    {
      'collection': 'message_status',
      'fields': [
        {'field': 'messageId', 'order': 'ASCENDING'},
        {'field': 'userId', 'order': 'ASCENDING'},
      ],
    },
    // User presence
    {
      'collection': 'user_presence',
      'fields': [
        {'field': 'userId', 'order': 'ASCENDING'},
        {'field': 'lastSeen', 'order': 'DESCENDING'},
      ],
    },
    // Typing indicators
    {
      'collection': 'typing_indicators',
      'fields': [
        {'field': 'chatId', 'order': 'ASCENDING'},
        {'field': 'updatedAt', 'order': 'DESCENDING'},
      ],
    },
  ];

  /// Firestore security rules recommendations
  static const String securityRulesTemplate = '''
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Chat access rules
    match /chats/{chatId} {
      allow read, write: if request.auth != null 
        && request.auth.uid in resource.data.participantIds;
    }
    
    // Message access rules
    match /chat_messages/{messageId} {
      allow read: if request.auth != null 
        && exists(/databases/\$(database)/documents/chats/\$(resource.data.chatId))
        && request.auth.uid in get(/databases/\$(database)/documents/chats/\$(resource.data.chatId)).data.participantIds;
      
      allow create: if request.auth != null 
        && request.auth.uid == request.resource.data.senderId
        && exists(/databases/\$(database)/documents/chats/\$(request.resource.data.chatId))
        && request.auth.uid in get(/databases/\$(database)/documents/chats/\$(request.resource.data.chatId)).data.participantIds;
      
      allow update: if request.auth != null 
        && request.auth.uid == resource.data.senderId
        && request.resource.data.senderId == resource.data.senderId;
    }
    
    // User presence rules
    match /user_presence/{userId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Typing indicators rules
    match /typing_indicators/{indicatorId} {
      allow read, write: if request.auth != null 
        && exists(/databases/\$(database)/documents/chats/\$(resource.data.chatId))
        && request.auth.uid in get(/databases/\$(database)/documents/chats/\$(resource.data.chatId)).data.participantIds;
    }
  }
}
''';

  /// Get optimized query for messages
  static Query getMessagesQuery(String chatId, {DocumentSnapshot? startAfter}) {
    var query = firestore
        .collection('chat_messages')
        .where('chatId', isEqualTo: chatId)
        .orderBy('sentAt', descending: true)
        .limit(messagesPageSize);

    if (startAfter != null) {
      query = query.startAfterDocument(startAfter);
    }

    return query;
  }

  /// Get optimized query for user chats
  static Query getUserChatsQuery(String userId) {
    return firestore
        .collection('chats')
        .where('participantIds', arrayContains: userId)
        .where('isActive', isEqualTo: true)
        .orderBy('lastMessageAt', descending: true);
  }

  /// Get optimized query for typing indicators
  static Query getTypingIndicatorsQuery(String chatId) {
    return firestore
        .collection('typing_indicators')
        .where('chatId', isEqualTo: chatId)
        .where('updatedAt', isGreaterThan: 
          Timestamp.fromDate(DateTime.now().subtract(typingTimeout)));
  }

  /// Batch write configuration
  static WriteBatch createBatch() {
    return firestore.batch();
  }

  /// Transaction configuration
  static Future<T> runTransaction<T>(
    TransactionHandler<T> updateFunction, {
    Duration timeout = const Duration(seconds: 30),
  }) {
    return firestore.runTransaction(
      updateFunction,
      timeout: timeout,
    );
  }
}
