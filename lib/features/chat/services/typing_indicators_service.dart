import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logger/logger.dart';

import '../config/firebase_chat_config.dart';
import '../../../core/constants/firebase_collections.dart';

/// Service for managing typing indicators in real-time
class TypingIndicatorsService {
  static final TypingIndicatorsService _instance = TypingIndicatorsService._internal();
  factory TypingIndicatorsService() => _instance;
  TypingIndicatorsService._internal();

  final Logger _logger = Logger();
  final Map<String, Timer> _typingTimers = {};
  final Map<String, StreamSubscription> _typingSubscriptions = {};

  /// Start typing indicator for a user in a chat
  Future<void> startTyping(String chatId, String userId, String userName) async {
    try {
      _logger.d('Starting typing indicator for user: $userId in chat: $chatId');

      final typingDoc = FirebaseChatConfig.firestore
          .collection(FirebaseCollections.typingIndicators)
          .doc('${chatId}_$userId');

      await typingDoc.set({
        'chatId': chatId,
        'userId': userId,
        'userName': userName,
        'isTyping': true,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Set auto-stop timer
      _setTypingTimer(chatId, userId);
      
      _logger.d('Successfully started typing indicator');
    } catch (e, stackTrace) {
      _logger.e('Error starting typing indicator: $e', 
                error: e, stackTrace: stackTrace);
    }
  }

  /// Stop typing indicator for a user in a chat
  Future<void> stopTyping(String chatId, String userId) async {
    try {
      _logger.d('Stopping typing indicator for user: $userId in chat: $chatId');

      final typingDoc = FirebaseChatConfig.firestore
          .collection(FirebaseCollections.typingIndicators)
          .doc('${chatId}_$userId');

      await typingDoc.delete();

      // Cancel timer
      final timerKey = '${chatId}_$userId';
      _typingTimers[timerKey]?.cancel();
      _typingTimers.remove(timerKey);
      
      _logger.d('Successfully stopped typing indicator');
    } catch (e, stackTrace) {
      _logger.e('Error stopping typing indicator: $e', 
                error: e, stackTrace: stackTrace);
    }
  }

  /// Update typing indicator (refresh timestamp)
  Future<void> updateTyping(String chatId, String userId, String userName) async {
    try {
      _logger.d('Updating typing indicator for user: $userId in chat: $chatId');

      final typingDoc = FirebaseChatConfig.firestore
          .collection(FirebaseCollections.typingIndicators)
          .doc('${chatId}_$userId');

      await typingDoc.update({
        'userName': userName,
        'isTyping': true,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Reset timer
      _setTypingTimer(chatId, userId);
      
      _logger.d('Successfully updated typing indicator');
    } catch (e, stackTrace) {
      _logger.e('Error updating typing indicator: $e', 
                error: e, stackTrace: stackTrace);
    }
  }

  /// Get current typing users for a chat
  Future<List<Map<String, dynamic>>> getTypingUsers(String chatId) async {
    try {
      _logger.d('Getting typing users for chat: $chatId');

      final cutoffTime = Timestamp.fromDate(
        DateTime.now().subtract(FirebaseChatConfig.typingTimeout),
      );

      final query = FirebaseChatConfig.firestore
          .collection(FirebaseCollections.typingIndicators)
          .where('chatId', isEqualTo: chatId)
          .where('updatedAt', isGreaterThan: cutoffTime);

      final snapshot = await query.get();
      
      final typingUsers = snapshot.docs.map((doc) {
        final data = doc.data();
        return {
          'userId': data['userId'] as String,
          'userName': data['userName'] as String,
          'updatedAt': (data['updatedAt'] as Timestamp).toDate(),
        };
      }).toList();

      _logger.d('Found ${typingUsers.length} typing users in chat: $chatId');
      return typingUsers;
    } catch (e, stackTrace) {
      _logger.e('Error getting typing users: $e', 
                error: e, stackTrace: stackTrace);
      return [];
    }
  }

  /// Stream typing indicators for real-time updates
  Stream<List<Map<String, dynamic>>> streamTypingUsers(String chatId, {String? excludeUserId}) {
    try {
      _logger.d('Setting up typing indicators stream for chat: $chatId');

      final cutoffTime = Timestamp.fromDate(
        DateTime.now().subtract(FirebaseChatConfig.typingTimeout),
      );

      return FirebaseChatConfig.firestore
          .collection(FirebaseCollections.typingIndicators)
          .where('chatId', isEqualTo: chatId)
          .where('updatedAt', isGreaterThan: cutoffTime)
          .snapshots()
          .map((snapshot) {
        final typingUsers = <Map<String, dynamic>>[];
        
        for (final doc in snapshot.docs) {
          final data = doc.data();
          final userId = data['userId'] as String;
          
          // Exclude current user if specified
          if (excludeUserId != null && userId == excludeUserId) {
            continue;
          }
          
          // Check if still within timeout
          final updatedAt = (data['updatedAt'] as Timestamp).toDate();
          final isRecent = DateTime.now().difference(updatedAt) < 
              FirebaseChatConfig.typingTimeout;
          
          if (isRecent) {
            typingUsers.add({
              'userId': userId,
              'userName': data['userName'] as String,
              'updatedAt': updatedAt,
            });
          }
        }
        
        return typingUsers;
      });
    } catch (e) {
      _logger.e('Error setting up typing indicators stream: $e');
      rethrow;
    }
  }

  /// Debounced typing update to avoid too many writes
  void debouncedStartTyping(String chatId, String userId, String userName) {
    final timerKey = '${chatId}_$userId';
    
    // Cancel existing timer
    _typingTimers[timerKey]?.cancel();
    
    // Start typing immediately if not already typing
    startTyping(chatId, userId, userName);
    
    // Set debounced update timer
    _typingTimers[timerKey] = Timer.periodic(
      const Duration(seconds: 2),
      (timer) async {
        await updateTyping(chatId, userId, userName);
      },
    );
  }

  /// Stop debounced typing
  void debouncedStopTyping(String chatId, String userId) {
    final timerKey = '${chatId}_$userId';
    
    // Cancel timer
    _typingTimers[timerKey]?.cancel();
    _typingTimers.remove(timerKey);
    
    // Stop typing
    stopTyping(chatId, userId);
  }

  /// Set auto-stop timer for typing indicator
  void _setTypingTimer(String chatId, String userId) {
    final timerKey = '${chatId}_$userId';
    
    // Cancel existing timer
    _typingTimers[timerKey]?.cancel();
    
    // Set new timer to auto-stop typing
    _typingTimers[timerKey] = Timer(
      FirebaseChatConfig.typingTimeout,
      () async {
        await stopTyping(chatId, userId);
      },
    );
  }

  /// Clean up expired typing indicators
  Future<void> cleanupExpiredIndicators() async {
    try {
      _logger.i('Cleaning up expired typing indicators');

      final cutoffTime = Timestamp.fromDate(
        DateTime.now().subtract(FirebaseChatConfig.typingTimeout),
      );

      final query = FirebaseChatConfig.firestore
          .collection(FirebaseCollections.typingIndicators)
          .where('updatedAt', isLessThan: cutoffTime)
          .limit(100); // Process in batches

      final snapshot = await query.get();
      
      if (snapshot.docs.isEmpty) {
        _logger.d('No expired typing indicators to clean up');
        return;
      }

      final batch = FirebaseChatConfig.createBatch();
      for (final doc in snapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
      _logger.i('Cleaned up ${snapshot.docs.length} expired typing indicators');
    } catch (e, stackTrace) {
      _logger.e('Error cleaning up expired typing indicators: $e', 
                error: e, stackTrace: stackTrace);
    }
  }

  /// Stop all typing indicators for a user (when going offline)
  Future<void> stopAllTypingForUser(String userId) async {
    try {
      _logger.i('Stopping all typing indicators for user: $userId');

      final query = FirebaseChatConfig.firestore
          .collection(FirebaseCollections.typingIndicators)
          .where('userId', isEqualTo: userId);

      final snapshot = await query.get();
      
      if (snapshot.docs.isEmpty) {
        _logger.d('No typing indicators to stop for user: $userId');
        return;
      }

      final batch = FirebaseChatConfig.createBatch();
      for (final doc in snapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
      
      // Cancel all timers for this user
      final userTimers = _typingTimers.keys
          .where((key) => key.endsWith('_$userId'))
          .toList();
      
      for (final timerKey in userTimers) {
        _typingTimers[timerKey]?.cancel();
        _typingTimers.remove(timerKey);
      }
      
      _logger.i('Stopped ${snapshot.docs.length} typing indicators for user: $userId');
    } catch (e, stackTrace) {
      _logger.e('Error stopping all typing indicators for user: $e', 
                error: e, stackTrace: stackTrace);
    }
  }

  /// Get typing indicator statistics
  Map<String, dynamic> getTypingStats() {
    return {
      'activeTimers': _typingTimers.length,
      'activeSubscriptions': _typingSubscriptions.length,
      'timerKeys': _typingTimers.keys.toList(),
    };
  }

  /// Dispose of all timers and subscriptions
  void dispose() {
    _logger.i('Disposing typing indicators service');
    
    // Cancel all timers
    for (final timer in _typingTimers.values) {
      timer.cancel();
    }
    _typingTimers.clear();
    
    // Cancel all subscriptions
    for (final subscription in _typingSubscriptions.values) {
      subscription.cancel();
    }
    _typingSubscriptions.clear();
    
    _logger.d('Disposed typing indicators service');
  }
}
