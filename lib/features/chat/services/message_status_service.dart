import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logger/logger.dart';

import '../config/firebase_chat_config.dart';
import '../enums/message_status.dart';
import '../../../core/constants/firebase_collections.dart';

/// Service for tracking message delivery and read status
class MessageStatusService {
  static final MessageStatusService _instance = MessageStatusService._internal();
  factory MessageStatusService() => _instance;
  MessageStatusService._internal();

  final Logger _logger = Logger();
  final Map<String, Timer> _statusUpdateTimers = {};

  /// Update message status (sent, delivered, read)
  Future<void> updateMessageStatus({
    required String messageId,
    required String userId,
    required MessageStatus status,
    DateTime? timestamp,
  }) async {
    try {
      _logger.d('Updating message status: $messageId -> $status for user: $userId');

      final statusDoc = FirebaseChatConfig.firestore
          .collection(FirebaseCollections.messageStatus)
          .doc('${messageId}_$userId');

      final statusData = {
        'messageId': messageId,
        'userId': userId,
        'status': status.value,
        'timestamp': timestamp ?? DateTime.now(),
        'updatedAt': DateTime.now(),
      };

      await statusDoc.set(statusData, SetOptions(merge: true));
      
      _logger.d('Successfully updated message status');
    } catch (e, stackTrace) {
      _logger.e('Error updating message status: $e', 
                error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Mark message as delivered for a user
  Future<void> markAsDelivered(String messageId, String userId) async {
    await updateMessageStatus(
      messageId: messageId,
      userId: userId,
      status: MessageStatus.delivered,
    );
  }

  /// Mark message as read for a user
  Future<void> markAsRead(String messageId, String userId) async {
    await updateMessageStatus(
      messageId: messageId,
      userId: userId,
      status: MessageStatus.read,
    );
  }

  /// Mark multiple messages as read (batch operation)
  Future<void> markMultipleAsRead(List<String> messageIds, String userId) async {
    try {
      _logger.i('Marking ${messageIds.length} messages as read for user: $userId');

      final batch = FirebaseChatConfig.createBatch();
      final timestamp = DateTime.now();

      for (final messageId in messageIds) {
        final statusDoc = FirebaseChatConfig.firestore
            .collection(FirebaseCollections.messageStatus)
            .doc('${messageId}_$userId');

        batch.set(statusDoc, {
          'messageId': messageId,
          'userId': userId,
          'status': MessageStatus.read.value,
          'timestamp': timestamp,
          'updatedAt': timestamp,
        }, SetOptions(merge: true));
      }

      await batch.commit();
      _logger.i('Successfully marked ${messageIds.length} messages as read');
    } catch (e, stackTrace) {
      _logger.e('Error marking multiple messages as read: $e', 
                error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Get message status for specific users
  Future<Map<String, MessageStatus>> getMessageStatus(
    String messageId, 
    List<String> userIds,
  ) async {
    try {
      _logger.d('Getting message status for message: $messageId');

      final statusMap = <String, MessageStatus>{};
      
      // Get status documents for all users
      final futures = userIds.map((userId) async {
        final statusDoc = await FirebaseChatConfig.firestore
            .collection(FirebaseCollections.messageStatus)
            .doc('${messageId}_$userId')
            .get();

        if (statusDoc.exists) {
          final data = statusDoc.data() as Map<String, dynamic>;
          final statusValue = data['status'] as String;
          statusMap[userId] = MessageStatusExtension.fromString(statusValue);
        } else {
          statusMap[userId] = MessageStatus.sent; // Default status
        }
      });

      await Future.wait(futures);
      return statusMap;
    } catch (e, stackTrace) {
      _logger.e('Error getting message status: $e', 
                error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Get read receipts for a message
  Future<Map<String, DateTime>> getReadReceipts(String messageId) async {
    try {
      _logger.d('Getting read receipts for message: $messageId');

      final query = FirebaseChatConfig.firestore
          .collection(FirebaseCollections.messageStatus)
          .where('messageId', isEqualTo: messageId)
          .where('status', isEqualTo: MessageStatus.read.value);

      final snapshot = await query.get();
      final readReceipts = <String, DateTime>{};

      for (final doc in snapshot.docs) {
        final data = doc.data();
        final userId = data['userId'] as String;
        final timestamp = (data['timestamp'] as Timestamp).toDate();
        readReceipts[userId] = timestamp;
      }

      return readReceipts;
    } catch (e, stackTrace) {
      _logger.e('Error getting read receipts: $e', 
                error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Stream message status updates for real-time read receipts
  Stream<Map<String, MessageStatus>> streamMessageStatus(
    String messageId, 
    List<String> userIds,
  ) {
    try {
      _logger.d('Setting up message status stream for: $messageId');

      return FirebaseChatConfig.firestore
          .collection(FirebaseCollections.messageStatus)
          .where('messageId', isEqualTo: messageId)
          .snapshots()
          .map((snapshot) {
        final statusMap = <String, MessageStatus>{};
        
        // Initialize all users with sent status
        for (final userId in userIds) {
          statusMap[userId] = MessageStatus.sent;
        }
        
        // Update with actual status from documents
        for (final doc in snapshot.docs) {
          final data = doc.data();
          final userId = data['userId'] as String;
          final statusValue = data['status'] as String;
          
          if (userIds.contains(userId)) {
            statusMap[userId] = MessageStatusExtension.fromString(statusValue);
          }
        }
        
        return statusMap;
      });
    } catch (e) {
      _logger.e('Error setting up message status stream: $e');
      rethrow;
    }
  }

  /// Auto-mark messages as delivered when user comes online
  Future<void> autoMarkDelivered(String userId, List<String> messageIds) async {
    try {
      if (messageIds.isEmpty) return;

      _logger.i('Auto-marking ${messageIds.length} messages as delivered for user: $userId');

      final batch = FirebaseChatConfig.createBatch();
      final timestamp = DateTime.now();

      for (final messageId in messageIds) {
        final statusDoc = FirebaseChatConfig.firestore
            .collection(FirebaseCollections.messageStatus)
            .doc('${messageId}_$userId');

        // Only update if not already read
        batch.set(statusDoc, {
          'messageId': messageId,
          'userId': userId,
          'status': MessageStatus.delivered.value,
          'timestamp': timestamp,
          'updatedAt': timestamp,
        }, SetOptions(merge: true));
      }

      await batch.commit();
      _logger.i('Successfully auto-marked messages as delivered');
    } catch (e, stackTrace) {
      _logger.e('Error auto-marking messages as delivered: $e', 
                error: e, stackTrace: stackTrace);
    }
  }

  /// Debounced read status update to avoid too many writes
  void debouncedMarkAsRead(String messageId, String userId) {
    final key = '${messageId}_$userId';
    
    // Cancel existing timer
    _statusUpdateTimers[key]?.cancel();
    
    // Set new timer
    _statusUpdateTimers[key] = Timer(
      const Duration(milliseconds: 500),
      () async {
        await markAsRead(messageId, userId);
        _statusUpdateTimers.remove(key);
      },
    );
  }

  /// Get unread message count for a user in a chat
  Future<int> getUnreadCount(String chatId, String userId) async {
    try {
      _logger.d('Getting unread count for chat: $chatId, user: $userId');

      // Get all messages in chat
      final messagesQuery = FirebaseChatConfig.firestore
          .collection(FirebaseCollections.chatMessages)
          .where('chatId', isEqualTo: chatId)
          .where('senderId', isNotEqualTo: userId); // Exclude own messages

      final messagesSnapshot = await messagesQuery.get();
      final messageIds = messagesSnapshot.docs.map((doc) => doc.id).toList();

      if (messageIds.isEmpty) return 0;

      // Get read status for these messages
      final readQuery = FirebaseChatConfig.firestore
          .collection(FirebaseCollections.messageStatus)
          .where('userId', isEqualTo: userId)
          .where('status', isEqualTo: MessageStatus.read.value);

      final readSnapshot = await readQuery.get();
      final readMessageIds = readSnapshot.docs
          .map((doc) => doc.data()['messageId'] as String)
          .toSet();

      // Count unread messages
      final unreadCount = messageIds.where((id) => !readMessageIds.contains(id)).length;
      
      _logger.d('Unread count for chat $chatId: $unreadCount');
      return unreadCount;
    } catch (e, stackTrace) {
      _logger.e('Error getting unread count: $e', 
                error: e, stackTrace: stackTrace);
      return 0;
    }
  }

  /// Clean up old status records (for maintenance)
  Future<void> cleanupOldStatusRecords({Duration? olderThan}) async {
    try {
      final cutoffDate = DateTime.now().subtract(
        olderThan ?? const Duration(days: 30),
      );

      _logger.i('Cleaning up message status records older than: $cutoffDate');

      final query = FirebaseChatConfig.firestore
          .collection(FirebaseCollections.messageStatus)
          .where('updatedAt', isLessThan: cutoffDate)
          .limit(500); // Process in batches

      final snapshot = await query.get();
      
      if (snapshot.docs.isEmpty) {
        _logger.d('No old status records to clean up');
        return;
      }

      final batch = FirebaseChatConfig.createBatch();
      for (final doc in snapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
      _logger.i('Cleaned up ${snapshot.docs.length} old status records');
    } catch (e, stackTrace) {
      _logger.e('Error cleaning up old status records: $e', 
                error: e, stackTrace: stackTrace);
    }
  }

  /// Dispose of all timers
  void dispose() {
    for (final timer in _statusUpdateTimers.values) {
      timer.cancel();
    }
    _statusUpdateTimers.clear();
    _logger.d('Disposed message status service');
  }
}
