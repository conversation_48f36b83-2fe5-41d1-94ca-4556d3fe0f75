import 'dart:async';
import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logger/logger.dart';

/// Enhanced error handling service with retry logic and user feedback
class ChatErrorService {
  static final ChatErrorService _instance = ChatErrorService._internal();
  factory ChatErrorService() => _instance;
  ChatErrorService._internal();

  final Logger _logger = Logger();
  final Map<String, int> _retryAttempts = {};
  final Map<String, Timer> _retryTimers = {};

  /// Execute operation with retry logic and error handling
  Future<T> executeWithRetry<T>({
    required String operationId,
    required Future<T> Function() operation,
    int maxAttempts = 3,
    Duration initialDelay = const Duration(seconds: 1),
    double backoffMultiplier = 2.0,
    bool exponentialBackoff = true,
    List<Type>? retryableExceptions,
    String? context,
  }) async {
    final attempts = _retryAttempts[operationId] ?? 0;

    try {
      _logger.d(
        'Executing operation: $operationId (attempt ${attempts + 1}/$maxAttempts)',
      );

      final result = await operation();

      // Success - reset retry count
      _retryAttempts.remove(operationId);
      _retryTimers[operationId]?.cancel();
      _retryTimers.remove(operationId);

      _logger.d('Operation succeeded: $operationId');
      return result;
    } catch (e, stackTrace) {
      _logger.e(
        'Operation failed: $operationId - $e',
        error: e,
        stackTrace: stackTrace,
      );

      // Check if we should retry
      if (!_shouldRetry(e, attempts, maxAttempts, retryableExceptions)) {
        _retryAttempts.remove(operationId);
        _retryTimers[operationId]?.cancel();
        _retryTimers.remove(operationId);

        throw ChatOperationException(
          operationId: operationId,
          message: e.toString(),
          originalError: e,
          attempts: attempts + 1,
          context: context,
        );
      }

      // Calculate delay for next attempt
      final delay = _calculateDelay(
        attempts,
        initialDelay,
        backoffMultiplier,
        exponentialBackoff,
      );

      _retryAttempts[operationId] = attempts + 1;

      _logger.i(
        'Retrying operation: $operationId in ${delay.inMilliseconds}ms',
      );

      // Wait before retry
      await Future.delayed(delay);

      // Recursive retry
      return executeWithRetry<T>(
        operationId: operationId,
        operation: operation,
        maxAttempts: maxAttempts,
        initialDelay: initialDelay,
        backoffMultiplier: backoffMultiplier,
        exponentialBackoff: exponentialBackoff,
        retryableExceptions: retryableExceptions,
        context: context,
      );
    }
  }

  /// Execute operation with timeout
  Future<T> executeWithTimeout<T>({
    required Future<T> Function() operation,
    Duration timeout = const Duration(seconds: 30),
    String? operationName,
  }) async {
    try {
      return await operation().timeout(timeout);
    } on TimeoutException {
      final error = ChatTimeoutException(
        operationName: operationName ?? 'Unknown operation',
        timeout: timeout,
      );
      _logger.e('Operation timed out: $operationName');
      throw error;
    }
  }

  /// Handle Firebase-specific errors
  ChatException handleFirebaseError(dynamic error, {String? context}) {
    if (error is FirebaseException) {
      switch (error.code) {
        case 'permission-denied':
          return ChatPermissionException(
            message: 'You don\'t have permission to perform this action',
            originalError: error,
            context: context,
          );
        case 'not-found':
          return ChatNotFoundException(
            message: 'The requested resource was not found',
            originalError: error,
            context: context,
          );
        case 'already-exists':
          return ChatConflictException(
            message: 'The resource already exists',
            originalError: error,
            context: context,
          );
        case 'resource-exhausted':
          return ChatRateLimitException(
            message: 'Too many requests. Please try again later',
            originalError: error,
            context: context,
          );
        case 'unavailable':
          return ChatNetworkException(
            message: 'Service temporarily unavailable',
            originalError: error,
            context: context,
          );
        case 'deadline-exceeded':
          return ChatTimeoutException(
            operationName: context ?? 'Firebase operation',
            timeout: const Duration(seconds: 30),
            originalError: error,
          );
        default:
          return ChatFirebaseException(
            code: error.code,
            message: error.message ?? 'Unknown Firebase error',
            originalError: error,
            context: context,
          );
      }
    }

    return ChatGenericException(
      message: error.toString(),
      originalError: error,
      context: context,
    );
  }

  /// Check if error should be retried
  bool _shouldRetry(
    dynamic error,
    int attempts,
    int maxAttempts,
    List<Type>? retryableExceptions,
  ) {
    if (attempts >= maxAttempts) return false;

    // Default retryable exceptions
    final defaultRetryable = [
      TimeoutException,
      ChatTimeoutException,
      ChatNetworkException,
      ChatRateLimitException,
    ];

    final retryable = retryableExceptions ?? defaultRetryable;

    // Check if error type is retryable
    for (final type in retryable) {
      if (error.runtimeType == type) return true;
    }

    // Check Firebase-specific retryable errors
    if (error is FirebaseException) {
      final retryableCodes = [
        'unavailable',
        'deadline-exceeded',
        'resource-exhausted',
        'internal',
        'aborted',
      ];
      return retryableCodes.contains(error.code);
    }

    return false;
  }

  /// Calculate delay for retry with backoff
  Duration _calculateDelay(
    int attempts,
    Duration initialDelay,
    double backoffMultiplier,
    bool exponentialBackoff,
  ) {
    if (!exponentialBackoff) {
      return initialDelay;
    }

    final multiplier = pow(backoffMultiplier, attempts);
    final delayMs = (initialDelay.inMilliseconds * multiplier).round();

    // Add jitter to prevent thundering herd
    final jitter = Random().nextDouble() * 0.1; // 10% jitter
    final finalDelayMs = (delayMs * (1 + jitter)).round();

    // Cap maximum delay at 30 seconds
    return Duration(milliseconds: min(finalDelayMs, 30000));
  }

  /// Get retry statistics
  Map<String, dynamic> getRetryStats() {
    return {
      'activeRetries': _retryAttempts.length,
      'retryAttempts': Map.from(_retryAttempts),
      'activeTimers': _retryTimers.length,
    };
  }

  /// Clear retry state for an operation
  void clearRetryState(String operationId) {
    _retryAttempts.remove(operationId);
    _retryTimers[operationId]?.cancel();
    _retryTimers.remove(operationId);
  }

  /// Clear all retry states
  void clearAllRetryStates() {
    _retryAttempts.clear();
    for (final timer in _retryTimers.values) {
      timer.cancel();
    }
    _retryTimers.clear();
  }

  /// Dispose of all resources
  void dispose() {
    clearAllRetryStates();
    _logger.d('Disposed chat error service');
  }
}

/// Base class for chat-related exceptions
abstract class ChatException implements Exception {
  final String message;
  final dynamic originalError;
  final String? context;
  final DateTime timestamp;

  ChatException({required this.message, this.originalError, this.context})
    : timestamp = DateTime.now();

  @override
  String toString() {
    return 'ChatException: $message${context != null ? ' (Context: $context)' : ''}';
  }

  /// Get user-friendly error message
  String get userMessage => message;

  /// Get error severity level
  ErrorSeverity get severity => ErrorSeverity.error;
}

/// Operation-specific exception with retry information
class ChatOperationException extends ChatException {
  final String operationId;
  final int attempts;

  ChatOperationException({
    required this.operationId,
    required this.attempts,
    required super.message,
    super.originalError,
    super.context,
  });

  @override
  String get userMessage =>
      'Operation failed after $attempts attempts. Please try again.';
}

/// Timeout-specific exception
class ChatTimeoutException extends ChatException {
  final String operationName;
  final Duration timeout;

  ChatTimeoutException({
    required this.operationName,
    required this.timeout,
    super.originalError,
  }) : super(
         message:
             'Operation "$operationName" timed out after ${timeout.inSeconds}s',
       );

  @override
  String get userMessage =>
      'The operation is taking longer than expected. Please check your connection and try again.';
}

/// Network-related exception
class ChatNetworkException extends ChatException {
  ChatNetworkException({
    required super.message,
    super.originalError,
    super.context,
  });

  @override
  String get userMessage =>
      'Network error. Please check your internet connection and try again.';
}

/// Permission-related exception
class ChatPermissionException extends ChatException {
  ChatPermissionException({
    required super.message,
    super.originalError,
    super.context,
  });

  @override
  String get userMessage =>
      'You don\'t have permission to perform this action.';

  @override
  ErrorSeverity get severity => ErrorSeverity.warning;
}

/// Resource not found exception
class ChatNotFoundException extends ChatException {
  ChatNotFoundException({
    required super.message,
    super.originalError,
    super.context,
  });

  @override
  String get userMessage => 'The requested item could not be found.';
}

/// Resource conflict exception
class ChatConflictException extends ChatException {
  ChatConflictException({
    required super.message,
    super.originalError,
    super.context,
  });

  @override
  String get userMessage => 'This action conflicts with existing data.';
}

/// Rate limiting exception
class ChatRateLimitException extends ChatException {
  ChatRateLimitException({
    required super.message,
    super.originalError,
    super.context,
  });

  @override
  String get userMessage =>
      'Too many requests. Please wait a moment and try again.';

  @override
  ErrorSeverity get severity => ErrorSeverity.warning;
}

/// Firebase-specific exception
class ChatFirebaseException extends ChatException {
  final String code;

  ChatFirebaseException({
    required this.code,
    required super.message,
    super.originalError,
    super.context,
  });

  @override
  String get userMessage => 'A service error occurred. Please try again later.';
}

/// Generic exception for unknown errors
class ChatGenericException extends ChatException {
  ChatGenericException({
    required super.message,
    super.originalError,
    super.context,
  });

  @override
  String get userMessage => 'An unexpected error occurred. Please try again.';
}

/// Error severity levels
enum ErrorSeverity { info, warning, error, critical }
