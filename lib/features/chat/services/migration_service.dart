import 'dart:async';

import 'package:logger/logger.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../config/firebase_chat_config.dart';
import '../../../core/constants/firebase_collections.dart';

/// Service for preparing and managing migration from Firebase to new architecture
class MigrationService {
  static final MigrationService _instance = MigrationService._internal();
  factory MigrationService() => _instance;
  MigrationService._internal();

  final Logger _logger = Logger();
  bool _dualWriteEnabled = false;
  bool _migrationMode = false;
  final Map<String, dynamic> _migrationStats = {};

  /// Enable dual-write mode for gradual migration
  void enableDualWrite() {
    _dualWriteEnabled = true;
    _logger.i('Dual-write mode enabled for migration');
  }

  /// Disable dual-write mode
  void disableDualWrite() {
    _dualWriteEnabled = false;
    _logger.i('Dual-write mode disabled');
  }

  /// Check if dual-write is enabled
  bool get isDualWriteEnabled => _dualWriteEnabled;

  /// Enable migration mode
  void enableMigrationMode() {
    _migrationMode = true;
    _logger.i('Migration mode enabled');
  }

  /// Disable migration mode
  void disableMigrationMode() {
    _migrationMode = false;
    _logger.i('Migration mode disabled');
  }

  /// Check if in migration mode
  bool get isMigrationMode => _migrationMode;

  /// Export chat data for migration
  Future<Map<String, dynamic>> exportChatData({
    String? chatId,
    DateTime? startDate,
    DateTime? endDate,
    int? batchSize,
  }) async {
    try {
      _logger.i('Starting chat data export');
      
      final exportData = <String, dynamic>{
        'metadata': {
          'exportedAt': DateTime.now().toIso8601String(),
          'version': '1.0',
          'format': 'firebase_to_new_architecture',
        },
        'chats': [],
        'messages': [],
        'participants': [],
        'statistics': {},
      };

      // Export chats
      final chats = await _exportChats(chatId: chatId, batchSize: batchSize);
      exportData['chats'] = chats;

      // Export messages
      final messages = await _exportMessages(
        chatId: chatId,
        startDate: startDate,
        endDate: endDate,
        batchSize: batchSize,
      );
      exportData['messages'] = messages;

      // Export participants
      final participants = await _exportParticipants(chatId: chatId);
      exportData['participants'] = participants;

      // Generate statistics
      exportData['statistics'] = {
        'totalChats': chats.length,
        'totalMessages': messages.length,
        'totalParticipants': participants.length,
        'dateRange': {
          'start': startDate?.toIso8601String(),
          'end': endDate?.toIso8601String(),
        },
      };

      _logger.i('Chat data export completed: ${exportData['statistics']}');
      return exportData;
    } catch (e, stackTrace) {
      _logger.e('Error exporting chat data: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Export chats collection
  Future<List<Map<String, dynamic>>> _exportChats({
    String? chatId,
    int? batchSize,
  }) async {
    final chats = <Map<String, dynamic>>[];
    
    Query query = FirebaseChatConfig.firestore.collection(FirebaseCollections.chats);
    
    if (chatId != null) {
      query = query.where(FieldPath.documentId, isEqualTo: chatId);
    }
    
    if (batchSize != null) {
      query = query.limit(batchSize);
    }

    final snapshot = await query.get();
    
    for (final doc in snapshot.docs) {
      final data = doc.data() as Map<String, dynamic>;
      data['id'] = doc.id;
      
      // Transform data for new architecture
      final transformedData = _transformChatData(data);
      chats.add(transformedData);
    }

    _logger.d('Exported ${chats.length} chats');
    return chats;
  }

  /// Export messages collection
  Future<List<Map<String, dynamic>>> _exportMessages({
    String? chatId,
    DateTime? startDate,
    DateTime? endDate,
    int? batchSize,
  }) async {
    final messages = <Map<String, dynamic>>[];
    
    Query query = FirebaseChatConfig.firestore.collection(FirebaseCollections.chatMessages);
    
    if (chatId != null) {
      query = query.where('chatId', isEqualTo: chatId);
    }
    
    if (startDate != null) {
      query = query.where('sentAt', isGreaterThanOrEqualTo: startDate);
    }
    
    if (endDate != null) {
      query = query.where('sentAt', isLessThanOrEqualTo: endDate);
    }
    
    query = query.orderBy('sentAt');
    
    if (batchSize != null) {
      query = query.limit(batchSize);
    }

    final snapshot = await query.get();
    
    for (final doc in snapshot.docs) {
      final data = doc.data() as Map<String, dynamic>;
      data['id'] = doc.id;
      
      // Transform data for new architecture
      final transformedData = _transformMessageData(data);
      messages.add(transformedData);
    }

    _logger.d('Exported ${messages.length} messages');
    return messages;
  }

  /// Export participants collection
  Future<List<Map<String, dynamic>>> _exportParticipants({String? chatId}) async {
    final participants = <Map<String, dynamic>>[];
    
    Query query = FirebaseChatConfig.firestore.collection(FirebaseCollections.chatParticipants);
    
    if (chatId != null) {
      query = query.where('chatId', isEqualTo: chatId);
    }

    final snapshot = await query.get();
    
    for (final doc in snapshot.docs) {
      final data = doc.data() as Map<String, dynamic>;
      data['id'] = doc.id;
      
      // Transform data for new architecture
      final transformedData = _transformParticipantData(data);
      participants.add(transformedData);
    }

    _logger.d('Exported ${participants.length} participants');
    return participants;
  }

  /// Transform chat data for new architecture
  Map<String, dynamic> _transformChatData(Map<String, dynamic> firebaseData) {
    return {
      'id': firebaseData['id'],
      'type': firebaseData['type'],
      'name': firebaseData['title'],
      'description': firebaseData['metadata']?['description'],
      'participants': firebaseData['participantIds'] ?? [],
      'created_by': firebaseData['participants']?.entries
          .firstWhere((entry) => entry.value['role'] == 'admin', orElse: () => MapEntry('', {}))
          .key,
      'created_at': firebaseData['createdAt'],
      'updated_at': firebaseData['updatedAt'],
      'is_active': firebaseData['isActive'] ?? true,
      'settings': firebaseData['settings'],
      'last_message': {
        'id': firebaseData['lastMessageId'],
        'content': firebaseData['lastMessage']?['content'],
        'timestamp': firebaseData['lastMessageAt'],
        'sender_id': firebaseData['lastMessage']?['senderId'],
      },
      'metadata': {
        'firebase_id': firebaseData['id'],
        'migrated_at': DateTime.now().toIso8601String(),
        'migration_version': '1.0',
      },
    };
  }

  /// Transform message data for new architecture
  Map<String, dynamic> _transformMessageData(Map<String, dynamic> firebaseData) {
    return {
      'id': firebaseData['id'],
      'chat_id': firebaseData['chatId'],
      'sender_id': firebaseData['senderId'],
      'content': {
        'text': firebaseData['content'],
        'type': firebaseData['type'],
        'attachments': firebaseData['attachments'] ?? [],
      },
      'timestamp': firebaseData['sentAt'],
      'status': {
        'sent': firebaseData['sentAt'],
        'delivered': {},
        'read': {},
      },
      'reactions': firebaseData['reactions'] ?? {},
      'reply_to': firebaseData['replyToMessageId'],
      'edited_at': firebaseData['editedAt'],
      'deleted_at': firebaseData['isDeleted'] == true ? firebaseData['updatedAt'] : null,
      'metadata': {
        'firebase_id': firebaseData['id'],
        'migrated_at': DateTime.now().toIso8601String(),
        'migration_version': '1.0',
      },
    };
  }

  /// Transform participant data for new architecture
  Map<String, dynamic> _transformParticipantData(Map<String, dynamic> firebaseData) {
    return {
      'id': firebaseData['id'],
      'chat_id': firebaseData['chatId'],
      'user_id': firebaseData['userId'],
      'role': firebaseData['role'],
      'joined_at': firebaseData['joinedAt'],
      'left_at': firebaseData['leftAt'],
      'permissions': {
        'can_send_messages': firebaseData['canSendMessages'] ?? true,
        'can_add_participants': firebaseData['canAddParticipants'] ?? false,
      },
      'metadata': {
        'firebase_id': firebaseData['id'],
        'migrated_at': DateTime.now().toIso8601String(),
        'migration_version': '1.0',
      },
    };
  }

  /// Validate data consistency between Firebase and new system
  Future<Map<String, dynamic>> validateDataConsistency({
    required String chatId,
  }) async {
    try {
      _logger.i('Validating data consistency for chat: $chatId');
      
      final validationResults = <String, dynamic>{
        'chatId': chatId,
        'validatedAt': DateTime.now().toIso8601String(),
        'issues': [],
        'summary': {},
      };

      // Validate chat data
      final chatValidation = await _validateChatConsistency(chatId);
      validationResults['chat'] = chatValidation;

      // Validate messages data
      final messagesValidation = await _validateMessagesConsistency(chatId);
      validationResults['messages'] = messagesValidation;

      // Validate participants data
      final participantsValidation = await _validateParticipantsConsistency(chatId);
      validationResults['participants'] = participantsValidation;

      // Generate summary
      validationResults['summary'] = {
        'totalIssues': (chatValidation['issues'] as List).length +
                      (messagesValidation['issues'] as List).length +
                      (participantsValidation['issues'] as List).length,
        'isConsistent': (chatValidation['isConsistent'] as bool) &&
                       (messagesValidation['isConsistent'] as bool) &&
                       (participantsValidation['isConsistent'] as bool),
      };

      _logger.i('Data consistency validation completed: ${validationResults['summary']}');
      return validationResults;
    } catch (e, stackTrace) {
      _logger.e('Error validating data consistency: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Validate chat consistency
  Future<Map<String, dynamic>> _validateChatConsistency(String chatId) async {
    final issues = <String>[];
    
    // This would compare Firebase data with new system data
    // For now, just validate Firebase data integrity
    
    final chatDoc = await FirebaseChatConfig.firestore
        .collection(FirebaseCollections.chats)
        .doc(chatId)
        .get();

    if (!chatDoc.exists) {
      issues.add('Chat document not found in Firebase');
    } else {
      final data = chatDoc.data() as Map<String, dynamic>;
      
      if (data['participantIds'] == null || (data['participantIds'] as List).isEmpty) {
        issues.add('Chat has no participants');
      }
      
      if (data['title'] == null || (data['title'] as String).isEmpty) {
        issues.add('Chat has no title');
      }
    }

    return {
      'isConsistent': issues.isEmpty,
      'issues': issues,
    };
  }

  /// Validate messages consistency
  Future<Map<String, dynamic>> _validateMessagesConsistency(String chatId) async {
    final issues = <String>[];
    
    final messagesQuery = FirebaseChatConfig.firestore
        .collection(FirebaseCollections.chatMessages)
        .where('chatId', isEqualTo: chatId);

    final snapshot = await messagesQuery.get();
    
    for (final doc in snapshot.docs) {
      final data = doc.data();
      
      if (data['senderId'] == null) {
        issues.add('Message ${doc.id} has no sender');
      }
      
      if (data['content'] == null || (data['content'] as String).isEmpty) {
        issues.add('Message ${doc.id} has no content');
      }
      
      if (data['sentAt'] == null) {
        issues.add('Message ${doc.id} has no timestamp');
      }
    }

    return {
      'isConsistent': issues.isEmpty,
      'issues': issues,
      'messageCount': snapshot.docs.length,
    };
  }

  /// Validate participants consistency
  Future<Map<String, dynamic>> _validateParticipantsConsistency(String chatId) async {
    final issues = <String>[];
    
    final participantsQuery = FirebaseChatConfig.firestore
        .collection(FirebaseCollections.chatParticipants)
        .where('chatId', isEqualTo: chatId);

    final snapshot = await participantsQuery.get();
    
    for (final doc in snapshot.docs) {
      final data = doc.data();
      
      if (data['userId'] == null) {
        issues.add('Participant ${doc.id} has no user ID');
      }
      
      if (data['role'] == null) {
        issues.add('Participant ${doc.id} has no role');
      }
    }

    return {
      'isConsistent': issues.isEmpty,
      'issues': issues,
      'participantCount': snapshot.docs.length,
    };
  }

  /// Get migration statistics
  Map<String, dynamic> getMigrationStats() {
    return {
      'dualWriteEnabled': _dualWriteEnabled,
      'migrationMode': _migrationMode,
      'stats': Map.from(_migrationStats),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Update migration statistics
  void updateMigrationStats(String key, dynamic value) {
    _migrationStats[key] = value;
    _logger.d('Updated migration stat: $key = $value');
  }

  /// Generate migration report
  Future<Map<String, dynamic>> generateMigrationReport() async {
    try {
      _logger.i('Generating migration report');
      
      final report = <String, dynamic>{
        'generatedAt': DateTime.now().toIso8601String(),
        'system': {
          'dualWriteEnabled': _dualWriteEnabled,
          'migrationMode': _migrationMode,
        },
        'statistics': Map.from(_migrationStats),
        'recommendations': _generateRecommendations(),
      };

      return report;
    } catch (e, stackTrace) {
      _logger.e('Error generating migration report: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Generate migration recommendations
  List<String> _generateRecommendations() {
    final recommendations = <String>[];
    
    if (!_dualWriteEnabled && !_migrationMode) {
      recommendations.add('Consider enabling dual-write mode to prepare for migration');
    }
    
    if (_migrationStats.isEmpty) {
      recommendations.add('Start collecting migration statistics');
    }
    
    recommendations.add('Perform data consistency validation before migration');
    recommendations.add('Set up monitoring for migration progress');
    recommendations.add('Plan rollback strategy in case of issues');
    
    return recommendations;
  }

  /// Dispose of resources
  void dispose() {
    _migrationStats.clear();
    _logger.d('Disposed migration service');
  }
}
