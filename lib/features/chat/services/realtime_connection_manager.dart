import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

import '../config/firebase_chat_config.dart';
import '../models/message_model.dart';
import '../models/chat_model.dart';
import '../../../core/constants/firebase_collections.dart';

/// Manages real-time connections with optimization and error handling
class RealtimeConnectionManager {
  static final RealtimeConnectionManager _instance =
      RealtimeConnectionManager._internal();
  factory RealtimeConnectionManager() => _instance;
  RealtimeConnectionManager._internal();

  final Logger _logger = Logger();
  final Map<String, StreamSubscription> _activeSubscriptions = {};
  final Map<String, StreamController> _streamControllers = {};
  final Map<String, int> _retryAttempts = {};
  final Map<String, Timer> _reconnectTimers = {};

  /// Subscribe to real-time messages for a chat
  Stream<List<MessageModel>> subscribeToMessages(String chatId) {
    final subscriptionKey = 'messages_$chatId';

    // Return existing stream if already subscribed
    if (_streamControllers.containsKey(subscriptionKey)) {
      return _streamControllers[subscriptionKey]!.stream
          as Stream<List<MessageModel>>;
    }

    final controller = StreamController<List<MessageModel>>.broadcast();
    _streamControllers[subscriptionKey] = controller;

    _setupMessageSubscription(chatId, controller);

    return controller.stream;
  }

  /// Subscribe to real-time chat updates
  Stream<ChatModel?> subscribeToChat(String chatId) {
    final subscriptionKey = 'chat_$chatId';

    // Return existing stream if already subscribed
    if (_streamControllers.containsKey(subscriptionKey)) {
      return _streamControllers[subscriptionKey]!.stream as Stream<ChatModel?>;
    }

    final controller = StreamController<ChatModel?>.broadcast();
    _streamControllers[subscriptionKey] = controller;

    _setupChatSubscription(chatId, controller);

    return controller.stream;
  }

  /// Subscribe to user's chat list
  Stream<List<ChatModel>> subscribeToUserChats(String userId) {
    final subscriptionKey = 'user_chats_$userId';

    // Return existing stream if already subscribed
    if (_streamControllers.containsKey(subscriptionKey)) {
      return _streamControllers[subscriptionKey]!.stream
          as Stream<List<ChatModel>>;
    }

    final controller = StreamController<List<ChatModel>>.broadcast();
    _streamControllers[subscriptionKey] = controller;

    _setupUserChatsSubscription(userId, controller);

    return controller.stream;
  }

  /// Subscribe to typing indicators for a chat
  Stream<List<String>> subscribeToTypingIndicators(String chatId) {
    final subscriptionKey = 'typing_$chatId';

    // Return existing stream if already subscribed
    if (_streamControllers.containsKey(subscriptionKey)) {
      return _streamControllers[subscriptionKey]!.stream
          as Stream<List<String>>;
    }

    final controller = StreamController<List<String>>.broadcast();
    _streamControllers[subscriptionKey] = controller;

    _setupTypingSubscription(chatId, controller);

    return controller.stream;
  }

  /// Unsubscribe from a specific subscription
  void unsubscribe(String subscriptionKey) {
    _logger.d('Unsubscribing from: $subscriptionKey');

    _activeSubscriptions[subscriptionKey]?.cancel();
    _activeSubscriptions.remove(subscriptionKey);

    _streamControllers[subscriptionKey]?.close();
    _streamControllers.remove(subscriptionKey);

    _retryAttempts.remove(subscriptionKey);
    _reconnectTimers[subscriptionKey]?.cancel();
    _reconnectTimers.remove(subscriptionKey);
  }

  /// Unsubscribe from all connections
  void unsubscribeAll() {
    _logger.i('Unsubscribing from all real-time connections');

    for (final subscription in _activeSubscriptions.values) {
      subscription.cancel();
    }
    _activeSubscriptions.clear();

    for (final controller in _streamControllers.values) {
      controller.close();
    }
    _streamControllers.clear();

    for (final timer in _reconnectTimers.values) {
      timer.cancel();
    }
    _reconnectTimers.clear();

    _retryAttempts.clear();
  }

  /// Setup message subscription with error handling
  void _setupMessageSubscription(
    String chatId,
    StreamController<List<MessageModel>> controller,
  ) {
    final subscriptionKey = 'messages_$chatId';

    try {
      final query = FirebaseChatConfig.getMessagesQuery(
        chatId,
      ).limit(FirebaseChatConfig.messagesPageSize);

      final subscription = query.snapshots().listen(
        (snapshot) {
          try {
            final messages = snapshot.docs
                .map(
                  (doc) => MessageModel.fromJson({
                    ...doc.data() as Map<String, dynamic>,
                    'id': doc.id,
                  }),
                )
                .toList();

            controller.add(messages);
            _retryAttempts.remove(subscriptionKey);
            _logger.d(
              'Real-time messages updated for chat: $chatId (${messages.length} messages)',
            );
          } catch (e) {
            _logger.e('Error processing message snapshot for chat $chatId: $e');
            controller.addError(e);
          }
        },
        onError: (error) {
          _logger.e('Error in message subscription for chat $chatId: $error');
          controller.addError(error);
          _scheduleReconnect(
            subscriptionKey,
            () => _setupMessageSubscription(chatId, controller),
          );
        },
      );

      _activeSubscriptions[subscriptionKey] = subscription;
    } catch (e) {
      _logger.e('Error setting up message subscription for chat $chatId: $e');
      controller.addError(e);
    }
  }

  /// Setup chat subscription with error handling
  void _setupChatSubscription(
    String chatId,
    StreamController<ChatModel?> controller,
  ) {
    final subscriptionKey = 'chat_$chatId';

    try {
      final subscription = FirebaseChatConfig.firestore
          .collection(FirebaseCollections.chats)
          .doc(chatId)
          .snapshots()
          .listen(
            (snapshot) {
              try {
                if (snapshot.exists) {
                  final chat = ChatModel.fromJson({
                    ...snapshot.data() as Map<String, dynamic>,
                    'id': snapshot.id,
                  });
                  controller.add(chat);
                } else {
                  controller.add(null);
                }

                _retryAttempts.remove(subscriptionKey);
                _logger.d('Real-time chat updated: $chatId');
              } catch (e) {
                _logger.e('Error processing chat snapshot for $chatId: $e');
                controller.addError(e);
              }
            },
            onError: (error) {
              _logger.e('Error in chat subscription for $chatId: $error');
              controller.addError(error);
              _scheduleReconnect(
                subscriptionKey,
                () => _setupChatSubscription(chatId, controller),
              );
            },
          );

      _activeSubscriptions[subscriptionKey] = subscription;
    } catch (e) {
      _logger.e('Error setting up chat subscription for $chatId: $e');
      controller.addError(e);
    }
  }

  /// Setup user chats subscription with error handling
  void _setupUserChatsSubscription(
    String userId,
    StreamController<List<ChatModel>> controller,
  ) {
    final subscriptionKey = 'user_chats_$userId';

    try {
      final query = FirebaseChatConfig.getUserChatsQuery(userId);

      final subscription = query.snapshots().listen(
        (snapshot) {
          try {
            final chats = snapshot.docs
                .map(
                  (doc) => ChatModel.fromJson({
                    ...doc.data() as Map<String, dynamic>,
                    'id': doc.id,
                  }),
                )
                .toList();

            controller.add(chats);
            _retryAttempts.remove(subscriptionKey);
            _logger.d(
              'Real-time user chats updated for user: $userId (${chats.length} chats)',
            );
          } catch (e) {
            _logger.e('Error processing user chats snapshot for $userId: $e');
            controller.addError(e);
          }
        },
        onError: (error) {
          _logger.e('Error in user chats subscription for $userId: $error');
          controller.addError(error);
          _scheduleReconnect(
            subscriptionKey,
            () => _setupUserChatsSubscription(userId, controller),
          );
        },
      );

      _activeSubscriptions[subscriptionKey] = subscription;
    } catch (e) {
      _logger.e('Error setting up user chats subscription for $userId: $e');
      controller.addError(e);
    }
  }

  /// Setup typing indicators subscription
  void _setupTypingSubscription(
    String chatId,
    StreamController<List<String>> controller,
  ) {
    final subscriptionKey = 'typing_$chatId';

    try {
      final query = FirebaseChatConfig.getTypingIndicatorsQuery(chatId);

      final subscription = query.snapshots().listen(
        (snapshot) {
          try {
            final typingUsers = snapshot.docs
                .map((doc) {
                  final data = doc.data() as Map<String, dynamic>?;
                  return data?['userId'] as String?;
                })
                .where((userId) => userId != null)
                .cast<String>()
                .toList();

            controller.add(typingUsers);
            _retryAttempts.remove(subscriptionKey);
            _logger.d('Typing indicators updated for chat: $chatId');
          } catch (e) {
            _logger.e(
              'Error processing typing indicators for chat $chatId: $e',
            );
            controller.addError(e);
          }
        },
        onError: (error) {
          _logger.e('Error in typing subscription for chat $chatId: $error');
          controller.addError(error);
          _scheduleReconnect(
            subscriptionKey,
            () => _setupTypingSubscription(chatId, controller),
          );
        },
      );

      _activeSubscriptions[subscriptionKey] = subscription;
    } catch (e) {
      _logger.e('Error setting up typing subscription for chat $chatId: $e');
      controller.addError(e);
    }
  }

  /// Schedule reconnection with exponential backoff
  void _scheduleReconnect(
    String subscriptionKey,
    VoidCallback reconnectCallback,
  ) {
    final attempts = _retryAttempts[subscriptionKey] ?? 0;

    if (attempts >= FirebaseChatConfig.maxRetryAttempts) {
      _logger.w(
        'Max retry attempts reached for subscription: $subscriptionKey',
      );
      return;
    }

    _retryAttempts[subscriptionKey] = attempts + 1;

    final delay = Duration(
      seconds:
          FirebaseChatConfig.connectionRetryDelay.inSeconds * (attempts + 1),
    );

    _logger.i(
      'Scheduling reconnect for $subscriptionKey in ${delay.inSeconds}s (attempt ${attempts + 1})',
    );

    _reconnectTimers[subscriptionKey] = Timer(delay, () {
      _logger.i('Attempting to reconnect: $subscriptionKey');
      reconnectCallback();
    });
  }

  /// Get connection statistics
  Map<String, dynamic> getConnectionStats() {
    return {
      'activeSubscriptions': _activeSubscriptions.length,
      'activeControllers': _streamControllers.length,
      'retryAttempts': _retryAttempts,
      'pendingReconnects': _reconnectTimers.length,
    };
  }
}
