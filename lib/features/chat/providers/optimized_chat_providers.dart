import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../models/chat_model.dart';
import '../models/message_model.dart';
import '../enums/message_type.dart';
import '../enums/message_status.dart';
import '../repositories/chat_repository.dart';
import '../repositories/message_repository.dart';
import '../services/message_pagination_service.dart';
import '../services/realtime_connection_manager.dart';
import '../services/message_status_service.dart';
import '../services/typing_indicators_service.dart';
import '../services/chat_error_service.dart';
import '../../../core/providers/auth_providers.dart';

/// Optimized chat providers with caching, error handling, and performance improvements

// Auth-related providers
/// Provider for current user ID from authentication
final currentUserIdProvider = Provider<String>((ref) {
  // For testing purposes, use the test user ID
  // TODO: Remove this when authentication is fully implemented
  const testUserId = 'XRTanMcAUWSMq3mrRvve2Y9IMP12';

  final authState = ref.watch(authStateProvider);
  return authState.when(
    data: (state) => state.user?.id ?? testUserId,
    loading: () => testUserId,
    error: (_, __) => testUserId,
  );
});

// Service providers
final messagePaginationServiceProvider = Provider<MessagePaginationService>((
  ref,
) {
  return MessagePaginationService();
});

final realtimeConnectionManagerProvider = Provider<RealtimeConnectionManager>((
  ref,
) {
  return RealtimeConnectionManager();
});

final messageStatusServiceProvider = Provider<MessageStatusService>((ref) {
  return MessageStatusService();
});

final typingIndicatorsServiceProvider = Provider<TypingIndicatorsService>((
  ref,
) {
  return TypingIndicatorsService();
});

final chatErrorServiceProvider = Provider<ChatErrorService>((ref) {
  return ChatErrorService();
});

// Repository providers
final chatRepositoryProvider = Provider<ChatRepository>((ref) {
  return ChatRepository();
});

final messageRepositoryProvider = Provider<MessageRepository>((ref) {
  return MessageRepository();
});

/// Optimized user chats provider with caching and error handling
final userChatsOptimizedProvider = StreamProvider.autoDispose
    .family<List<ChatModel>, String>((ref, userId) {
      final connectionManager = ref.read(realtimeConnectionManagerProvider);
      final errorService = ref.read(chatErrorServiceProvider);
      final logger = Logger();

      return errorService
          .executeWithRetry(
            operationId: 'user_chats_$userId',
            operation: () async {
              logger.d('Setting up optimized user chats stream for: $userId');
              return connectionManager.subscribeToUserChats(userId);
            },
            context: 'User chats subscription',
          )
          .asStream()
          .asyncExpand((stream) => stream);
    });

/// Optimized chat details provider with caching
final chatDetailsOptimizedProvider = StreamProvider.autoDispose
    .family<ChatModel?, String>((ref, chatId) {
      final connectionManager = ref.read(realtimeConnectionManagerProvider);
      final errorService = ref.read(chatErrorServiceProvider);
      final logger = Logger();

      return errorService
          .executeWithRetry(
            operationId: 'chat_details_$chatId',
            operation: () async {
              logger.d('Setting up optimized chat details stream for: $chatId');
              return connectionManager.subscribeToChat(chatId);
            },
            context: 'Chat details subscription',
          )
          .asStream()
          .asyncExpand((stream) => stream);
    });

/// Optimized messages provider with pagination and caching
final messagesOptimizedProvider = StreamProvider.autoDispose
    .family<List<MessageModel>, String>((ref, chatId) {
      final connectionManager = ref.read(realtimeConnectionManagerProvider);
      final errorService = ref.read(chatErrorServiceProvider);
      final logger = Logger();

      return errorService
          .executeWithRetry(
            operationId: 'messages_$chatId',
            operation: () async {
              logger.d('Setting up optimized messages stream for: $chatId');
              return connectionManager.subscribeToMessages(chatId);
            },
            context: 'Messages subscription',
          )
          .asStream()
          .asyncExpand((stream) => stream);
    });

/// Typing indicators provider
final typingIndicatorsProvider = StreamProvider.autoDispose
    .family<List<Map<String, dynamic>>, String>((ref, chatId) {
      final typingService = ref.read(typingIndicatorsServiceProvider);
      final currentUserId = ref.watch(currentUserIdProvider);
      final logger = Logger();

      logger.d('Setting up typing indicators stream for chat: $chatId');
      return typingService.streamTypingUsers(
        chatId,
        excludeUserId: currentUserId,
      );
    });

/// Message status provider for read receipts
final messageStatusProvider = StreamProvider.autoDispose
    .family<Map<String, dynamic>, Map<String, dynamic>>((ref, params) {
      final messageId = params['messageId'] as String;
      final userIds = params['userIds'] as List<String>;
      final statusService = ref.read(messageStatusServiceProvider);
      final logger = Logger();

      logger.d('Setting up message status stream for: $messageId');
      return statusService
          .streamMessageStatus(messageId, userIds)
          .map((statusMap) => {'messageId': messageId, 'statusMap': statusMap});
    });

/// Chat operations provider for sending messages, etc.
class ChatOperationsNotifier extends StateNotifier<AsyncValue<void>> {
  ChatOperationsNotifier(this._ref) : super(const AsyncValue.data(null));

  final Ref _ref;
  final Logger _logger = Logger();

  /// Send message with optimized error handling
  Future<void> sendMessage({
    required String chatId,
    required String content,
    required String senderId,
    required String senderName,
  }) async {
    state = const AsyncValue.loading();

    final errorService = _ref.read(chatErrorServiceProvider);
    final messageRepository = _ref.read(messageRepositoryProvider);
    final paginationService = _ref.read(messagePaginationServiceProvider);

    try {
      await errorService.executeWithRetry(
        operationId: 'send_message_$chatId',
        operation: () async {
          _logger.i('Sending message to chat: $chatId');

          final message = MessageModel(
            id: '', // Will be set by repository
            chatId: chatId,
            senderId: senderId,
            senderName: senderName,
            type: MessageType.text,
            content: content,
            status: MessageStatus.sent,
            sentAt: DateTime.now(),
          );

          final sentMessage = await messageRepository.sendMessage(message);

          // Add to cache for immediate UI update
          paginationService.addMessageToCache(chatId, sentMessage);

          _logger.i('Successfully sent message: ${sentMessage.id}');
        },
        context: 'Send message',
        maxAttempts: 3,
      );

      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      _logger.e('Error sending message: $e', error: e, stackTrace: stackTrace);
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Load more messages with pagination
  Future<void> loadMoreMessages(String chatId) async {
    final errorService = _ref.read(chatErrorServiceProvider);
    final messageRepository = _ref.read(messageRepositoryProvider);

    try {
      await errorService.executeWithRetry(
        operationId: 'load_more_$chatId',
        operation: () async {
          _logger.i('Loading more messages for chat: $chatId');
          await messageRepository.loadMoreMessagesOptimized(chatId);
        },
        context: 'Load more messages',
      );
    } catch (e, stackTrace) {
      _logger.e(
        'Error loading more messages: $e',
        error: e,
        stackTrace: stackTrace,
      );
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Mark messages as read
  Future<void> markMessagesAsRead(
    List<String> messageIds,
    String userId,
  ) async {
    final errorService = _ref.read(chatErrorServiceProvider);
    final statusService = _ref.read(messageStatusServiceProvider);

    try {
      await errorService.executeWithRetry(
        operationId: 'mark_read_$userId',
        operation: () async {
          _logger.i('Marking ${messageIds.length} messages as read');
          await statusService.markMultipleAsRead(messageIds, userId);
        },
        context: 'Mark messages as read',
      );
    } catch (e, stackTrace) {
      _logger.e(
        'Error marking messages as read: $e',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// Start typing indicator
  Future<void> startTyping(
    String chatId,
    String userId,
    String userName,
  ) async {
    final typingService = _ref.read(typingIndicatorsServiceProvider);

    try {
      typingService.debouncedStartTyping(chatId, userId, userName);
    } catch (e) {
      _logger.e('Error starting typing indicator: $e');
    }
  }

  /// Stop typing indicator
  Future<void> stopTyping(String chatId, String userId) async {
    final typingService = _ref.read(typingIndicatorsServiceProvider);

    try {
      typingService.debouncedStopTyping(chatId, userId);
    } catch (e) {
      _logger.e('Error stopping typing indicator: $e');
    }
  }
}

final chatOperationsProvider =
    StateNotifierProvider<ChatOperationsNotifier, AsyncValue<void>>((ref) {
      return ChatOperationsNotifier(ref);
    });

/// Cache management provider
class CacheManagerNotifier extends StateNotifier<Map<String, dynamic>> {
  CacheManagerNotifier(this._ref) : super({});

  final Ref _ref;
  final Logger _logger = Logger();

  /// Clear cache for a specific chat
  void clearChatCache(String chatId) {
    final paginationService = _ref.read(messagePaginationServiceProvider);
    final messageRepository = _ref.read(messageRepositoryProvider);

    paginationService.clearChatCache(chatId);
    messageRepository.clearChatCache(chatId);

    state = {...state, 'lastCleared': DateTime.now().toIso8601String()};
    _logger.d('Cleared cache for chat: $chatId');
  }

  /// Clear all caches
  void clearAllCaches() {
    final paginationService = _ref.read(messagePaginationServiceProvider);
    final connectionManager = _ref.read(realtimeConnectionManagerProvider);

    paginationService.clearAllCaches();
    connectionManager.unsubscribeAll();

    state = {'allCachesCleared': DateTime.now().toIso8601String()};
    _logger.i('Cleared all chat caches');
  }

  /// Get cache statistics
  Map<String, dynamic> getCacheStats() {
    final paginationService = _ref.read(messagePaginationServiceProvider);
    final connectionManager = _ref.read(realtimeConnectionManagerProvider);

    return {
      'pagination': paginationService.getCacheStats(),
      'connections': connectionManager.getConnectionStats(),
      'lastUpdated': DateTime.now().toIso8601String(),
    };
  }
}

final cacheManagerProvider =
    StateNotifierProvider<CacheManagerNotifier, Map<String, dynamic>>((ref) {
      return CacheManagerNotifier(ref);
    });

/// Performance monitoring provider
final performanceStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final paginationService = ref.read(messagePaginationServiceProvider);
  final connectionManager = ref.read(realtimeConnectionManagerProvider);
  final errorService = ref.read(chatErrorServiceProvider);
  final typingService = ref.read(typingIndicatorsServiceProvider);

  return {
    'pagination': paginationService.getCacheStats(),
    'connections': connectionManager.getConnectionStats(),
    'retries': errorService.getRetryStats(),
    'typing': typingService.getTypingStats(),
    'timestamp': DateTime.now().toIso8601String(),
  };
});

/// Cleanup provider for disposing resources
final chatCleanupProvider = Provider<void>((ref) {
  ref.onDispose(() {
    final connectionManager = ref.read(realtimeConnectionManagerProvider);
    final errorService = ref.read(chatErrorServiceProvider);
    final typingService = ref.read(typingIndicatorsServiceProvider);
    final statusService = ref.read(messageStatusServiceProvider);

    connectionManager.unsubscribeAll();
    errorService.dispose();
    typingService.dispose();
    statusService.dispose();

    Logger().i('Disposed all chat resources');
  });
});
