import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logger/logger.dart';

import '../../../core/constants/firebase_collections.dart';
import '../models/chat_models.dart';
import '../enums/chat_enums.dart';
import '../services/message_pagination_service.dart';
import '../services/realtime_connection_manager.dart';
import '../config/firebase_chat_config.dart';

/// Repository for managing message data in Firebase Firestore
/// Optimized with pagination, caching, and real-time capabilities
class MessageRepository {
  static final FirebaseFirestore _firestore = FirebaseChatConfig.firestore;
  static final Logger _logger = Logger();
  static final MessagePaginationService _paginationService =
      MessagePaginationService();
  static final RealtimeConnectionManager _connectionManager =
      RealtimeConnectionManager();

  // Collection references
  static const String _messagesCollection = FirebaseCollections.chatMessages;
  static const String _reactionsCollection =
      FirebaseCollections.messageReactions;

  /// Get messages for a specific chat with pagination
  Future<List<MessageModel>> getMessagesForChat(
    String chatId, {
    int limit = 50,
    DocumentSnapshot? startAfter,
  }) async {
    try {
      _logger.i('Fetching messages for chat: $chatId (limit: $limit)');

      Query query = _firestore
          .collection(_messagesCollection)
          .where('chatId', isEqualTo: chatId)
          .where('isDeleted', isEqualTo: false)
          .orderBy('sentAt', descending: true)
          .limit(limit);

      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }

      final querySnapshot = await query.get();
      final messages = <MessageModel>[];

      for (final doc in querySnapshot.docs) {
        try {
          final data = doc.data() as Map<String, dynamic>;
          data['id'] = doc.id;
          final message = MessageModel.fromJson(data);
          messages.add(message);
        } catch (e) {
          _logger.e('Error parsing message document ${doc.id}: $e');
        }
      }

      _logger.i(
        'Successfully fetched ${messages.length} messages for chat $chatId',
      );
      return messages.reversed.toList(); // Reverse to show oldest first
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching messages for chat: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get initial messages for a chat with caching (optimized)
  Future<List<MessageModel>> getInitialMessagesOptimized(String chatId) async {
    try {
      _logger.i('Getting initial messages for chat: $chatId');

      // Try cache first
      final cachedMessages = _paginationService.getCachedMessages(chatId);
      if (cachedMessages != null) {
        _logger.d('Returning cached messages for chat: $chatId');
        return cachedMessages;
      }

      // Load from Firestore with pagination
      return await _paginationService.loadInitialMessages(chatId);
    } catch (e, stackTrace) {
      _logger.e(
        'Error getting initial messages for chat $chatId: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Load more messages for pagination (optimized)
  Future<List<MessageModel>> loadMoreMessagesOptimized(String chatId) async {
    try {
      _logger.i('Loading more messages for chat: $chatId');
      return await _paginationService.loadMoreMessages(chatId);
    } catch (e, stackTrace) {
      _logger.e(
        'Error loading more messages for chat $chatId: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Check if there are more messages to load
  bool hasMoreMessages(String chatId) {
    return _paginationService.hasMoreMessages(chatId);
  }

  /// Get real-time messages stream (optimized)
  Stream<List<MessageModel>> getMessagesStreamOptimized(String chatId) {
    try {
      _logger.i('Setting up real-time messages stream for chat: $chatId');
      return _connectionManager.subscribeToMessages(chatId);
    } catch (e) {
      _logger.e('Error setting up messages stream for chat $chatId: $e');
      rethrow;
    }
  }

  /// Clear cache for a specific chat
  void clearChatCache(String chatId) {
    _paginationService.clearChatCache(chatId);
    _logger.d('Cleared cache for chat: $chatId');
  }

  /// Get a specific message by ID
  Future<MessageModel?> getMessageById(String messageId) async {
    try {
      _logger.i('Fetching message by ID: $messageId');

      final doc = await _firestore
          .collection(_messagesCollection)
          .doc(messageId)
          .get();

      if (!doc.exists) {
        _logger.w('Message not found: $messageId');
        return null;
      }

      final data = doc.data()!;
      data['id'] = doc.id;
      final message = MessageModel.fromJson(data);

      _logger.i('Successfully fetched message: $messageId');
      return message;
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching message by ID: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Send a new message
  Future<MessageModel> sendMessage(MessageModel message) async {
    try {
      _logger.i('Sending message to chat: ${message.chatId}');

      final docRef = _firestore.collection(_messagesCollection).doc();
      final messageWithId = MessageModel(
        id: docRef.id,
        chatId: message.chatId,
        senderId: message.senderId,
        senderName: message.senderName,
        type: message.type,
        content: message.content,
        attachments: message.attachments,
        replyToMessageId: message.replyToMessageId,
        reactions: message.reactions,
        status: MessageStatus.sent,
        sentAt: DateTime.now(),
        isDeleted: false,
      );

      await docRef.set(messageWithId.toJson());

      _logger.i('Successfully sent message: ${messageWithId.id}');
      return messageWithId;
    } catch (e, stackTrace) {
      _logger.e('Error sending message: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Update message content (for editing)
  Future<MessageModel> updateMessage(
    String messageId,
    String newContent,
  ) async {
    try {
      _logger.i('Updating message: $messageId');

      await _firestore.collection(_messagesCollection).doc(messageId).update({
        'content': newContent,
        'editedAt': DateTime.now().toIso8601String(),
      });

      // Fetch and return updated message
      final updatedMessage = await getMessageById(messageId);
      if (updatedMessage == null) {
        throw Exception('Message not found after update: $messageId');
      }

      _logger.i('Successfully updated message: $messageId');
      return updatedMessage;
    } catch (e, stackTrace) {
      _logger.e('Error updating message: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Delete a message (soft delete)
  Future<void> deleteMessage(String messageId) async {
    try {
      _logger.i('Deleting message: $messageId');

      await _firestore.collection(_messagesCollection).doc(messageId).update({
        'isDeleted': true,
        'content': '[Message deleted]',
        'editedAt': DateTime.now().toIso8601String(),
      });

      _logger.i('Successfully deleted message: $messageId');
    } catch (e, stackTrace) {
      _logger.e('Error deleting message: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Add reaction to a message
  Future<void> addReaction(
    String messageId,
    String userId,
    String emoji,
  ) async {
    try {
      _logger.i('Adding reaction to message: $messageId');

      final reactionId = '${messageId}_${userId}_$emoji';
      final reaction = MessageReaction(
        id: reactionId,
        messageId: messageId,
        userId: userId,
        userName: 'User $userId', // TODO: Get from user profile
        emoji: emoji,
        createdAt: DateTime.now(),
      );

      await _firestore
          .collection(_reactionsCollection)
          .doc(reactionId)
          .set(reaction.toJson());

      _logger.i('Successfully added reaction to message: $messageId');
    } catch (e, stackTrace) {
      _logger.e('Error adding reaction: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Remove reaction from a message
  Future<void> removeReaction(
    String messageId,
    String userId,
    String emoji,
  ) async {
    try {
      _logger.i('Removing reaction from message: $messageId');

      final reactionId = '${messageId}_${userId}_$emoji';
      await _firestore
          .collection(_reactionsCollection)
          .doc(reactionId)
          .delete();

      _logger.i('Successfully removed reaction from message: $messageId');
    } catch (e, stackTrace) {
      _logger.e(
        'Error removing reaction: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get reactions for a message
  Future<List<MessageReaction>> getReactionsForMessage(String messageId) async {
    try {
      _logger.i('Fetching reactions for message: $messageId');

      final querySnapshot = await _firestore
          .collection(_reactionsCollection)
          .where('messageId', isEqualTo: messageId)
          .orderBy('createdAt')
          .get();

      final reactions = <MessageReaction>[];

      for (final doc in querySnapshot.docs) {
        try {
          final data = doc.data();
          data['id'] = doc.id;
          final reaction = MessageReaction.fromJson(data);
          reactions.add(reaction);
        } catch (e) {
          _logger.e('Error parsing reaction document ${doc.id}: $e');
        }
      }

      _logger.i(
        'Successfully fetched ${reactions.length} reactions for message $messageId',
      );
      return reactions;
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching reactions: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Update message status (read, delivered, etc.)
  Future<void> updateMessageStatus(
    String messageId,
    MessageStatus status,
  ) async {
    try {
      _logger.i('Updating message status: $messageId to ${status.value}');

      await _firestore.collection(_messagesCollection).doc(messageId).update({
        'status': status.value,
      });

      _logger.i('Successfully updated message status: $messageId');
    } catch (e, stackTrace) {
      _logger.e(
        'Error updating message status: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get real-time stream of messages for a chat
  Stream<List<MessageModel>> getMessagesStreamForChat(
    String chatId, {
    int limit = 50,
  }) {
    try {
      _logger.i('Setting up real-time stream for chat messages: $chatId');

      return _firestore
          .collection(_messagesCollection)
          .where('chatId', isEqualTo: chatId)
          .where('isDeleted', isEqualTo: false)
          .orderBy('sentAt', descending: true)
          .limit(limit)
          .snapshots()
          .map((snapshot) {
            final messages = <MessageModel>[];

            for (final doc in snapshot.docs) {
              try {
                final data = doc.data();
                data['id'] = doc.id;
                final message = MessageModel.fromJson(data);
                messages.add(message);
              } catch (e) {
                _logger.e('Error parsing message document ${doc.id}: $e');
              }
            }

            _logger.d(
              'Real-time update: ${messages.length} messages for chat $chatId',
            );
            return messages.reversed.toList(); // Reverse to show oldest first
          });
    } catch (e) {
      _logger.e('Error setting up messages stream: $e');
      rethrow;
    }
  }

  /// Search messages in a chat
  Future<List<MessageModel>> searchMessagesInChat(
    String chatId,
    String searchQuery, {
    int limit = 20,
  }) async {
    try {
      _logger.i('Searching messages in chat: $chatId for "$searchQuery"');

      // Note: Firestore doesn't support full-text search natively
      // This is a basic implementation that searches for exact matches
      // For production, consider using Algolia or similar service
      final querySnapshot = await _firestore
          .collection(_messagesCollection)
          .where('chatId', isEqualTo: chatId)
          .where('isDeleted', isEqualTo: false)
          .orderBy('sentAt', descending: true)
          .limit(limit * 5) // Get more to filter locally
          .get();

      final messages = <MessageModel>[];

      for (final doc in querySnapshot.docs) {
        try {
          final data = doc.data();
          data['id'] = doc.id;
          final message = MessageModel.fromJson(data);

          // Simple text search - case insensitive
          if (message.content.toLowerCase().contains(
            searchQuery.toLowerCase(),
          )) {
            messages.add(message);
            if (messages.length >= limit) break;
          }
        } catch (e) {
          _logger.e('Error parsing message document ${doc.id}: $e');
        }
      }

      _logger.i(
        'Successfully found ${messages.length} messages matching "$searchQuery"',
      );
      return messages;
    } catch (e, stackTrace) {
      _logger.e(
        'Error searching messages: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }
}
