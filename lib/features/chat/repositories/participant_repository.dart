import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logger/logger.dart';

import '../../../core/constants/firebase_collections.dart';
import '../models/chat_models.dart';
import '../enums/chat_enums.dart';

/// Repository for managing chat participant data in Firebase Firestore
class ParticipantRepository {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final Logger _logger = Logger();

  // Collection references
  static const String _chatsCollection = FirebaseCollections.chats;
  static const String _participantsCollection = FirebaseCollections.chatParticipants;

  /// Get participants for a specific chat
  Future<List<ChatParticipant>> getParticipantsForChat(String chatId) async {
    try {
      _logger.i('Fetching participants for chat: $chatId');

      final querySnapshot = await _firestore
          .collection(_participantsCollection)
          .where('chatId', isEqualTo: chatId)
          .orderBy('joinedAt')
          .get();

      final participants = <ChatParticipant>[];

      for (final doc in querySnapshot.docs) {
        try {
          final data = doc.data();
          data['id'] = doc.id;
          final participant = ChatParticipant.fromJson(data);
          participants.add(participant);
        } catch (e) {
          _logger.e('Error parsing participant document ${doc.id}: $e');
        }
      }

      _logger.i(
        'Successfully fetched ${participants.length} participants for chat $chatId',
      );
      return participants;
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching participants for chat: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Add a participant to a chat
  Future<void> addParticipantToChat(
    String chatId,
    ChatParticipant participant,
  ) async {
    try {
      _logger.i('Adding participant ${participant.userId} to chat: $chatId');

      // Start a batch operation to update both collections
      final batch = _firestore.batch();

      // Add to participants collection
      final participantRef = _firestore
          .collection(_participantsCollection)
          .doc('${chatId}_${participant.userId}');

      final participantData = participant.toJson();
      participantData['chatId'] = chatId;
      batch.set(participantRef, participantData);

      // Update chat document to include participant ID
      final chatRef = _firestore.collection(_chatsCollection).doc(chatId);
      batch.update(chatRef, {
        'participantIds': FieldValue.arrayUnion([participant.userId]),
        'participants.${participant.userId}': participant.toJson(),
        'updatedAt': DateTime.now().toIso8601String(),
      });

      await batch.commit();

      _logger.i(
        'Successfully added participant ${participant.userId} to chat $chatId',
      );
    } catch (e, stackTrace) {
      _logger.e(
        'Error adding participant to chat: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Remove a participant from a chat
  Future<void> removeParticipantFromChat(
    String chatId,
    String userId,
  ) async {
    try {
      _logger.i('Removing participant $userId from chat: $chatId');

      // Start a batch operation to update both collections
      final batch = _firestore.batch();

      // Remove from participants collection
      final participantRef = _firestore
          .collection(_participantsCollection)
          .doc('${chatId}_$userId');
      batch.delete(participantRef);

      // Update chat document to remove participant ID
      final chatRef = _firestore.collection(_chatsCollection).doc(chatId);
      batch.update(chatRef, {
        'participantIds': FieldValue.arrayRemove([userId]),
        'participants.$userId': FieldValue.delete(),
        'updatedAt': DateTime.now().toIso8601String(),
      });

      await batch.commit();

      _logger.i('Successfully removed participant $userId from chat $chatId');
    } catch (e, stackTrace) {
      _logger.e(
        'Error removing participant from chat: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Update participant role in a chat
  Future<void> updateParticipantRole(
    String chatId,
    String userId,
    ChatRole newRole,
  ) async {
    try {
      _logger.i(
        'Updating participant $userId role to ${newRole.value} in chat: $chatId',
      );

      // Start a batch operation to update both collections
      final batch = _firestore.batch();

      // Update in participants collection
      final participantRef = _firestore
          .collection(_participantsCollection)
          .doc('${chatId}_$userId');
      batch.update(participantRef, {
        'role': newRole.value,
      });

      // Update in chat document
      final chatRef = _firestore.collection(_chatsCollection).doc(chatId);
      batch.update(chatRef, {
        'participants.$userId.role': newRole.value,
        'updatedAt': DateTime.now().toIso8601String(),
      });

      await batch.commit();

      _logger.i(
        'Successfully updated participant $userId role in chat $chatId',
      );
    } catch (e, stackTrace) {
      _logger.e(
        'Error updating participant role: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Update participant permissions
  Future<void> updateParticipantPermissions(
    String chatId,
    String userId,
    Map<String, bool> permissions,
  ) async {
    try {
      _logger.i(
        'Updating participant $userId permissions in chat: $chatId',
      );

      // Start a batch operation to update both collections
      final batch = _firestore.batch();

      // Update in participants collection
      final participantRef = _firestore
          .collection(_participantsCollection)
          .doc('${chatId}_$userId');
      batch.update(participantRef, permissions);

      // Update in chat document
      final chatUpdates = <String, dynamic>{};
      permissions.forEach((key, value) {
        chatUpdates['participants.$userId.$key'] = value;
      });
      chatUpdates['updatedAt'] = DateTime.now().toIso8601String();

      final chatRef = _firestore.collection(_chatsCollection).doc(chatId);
      batch.update(chatRef, chatUpdates);

      await batch.commit();

      _logger.i(
        'Successfully updated participant $userId permissions in chat $chatId',
      );
    } catch (e, stackTrace) {
      _logger.e(
        'Error updating participant permissions: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Update participant's last read timestamp
  Future<void> updateLastReadAt(
    String chatId,
    String userId,
    DateTime timestamp,
  ) async {
    try {
      _logger.i('Updating last read timestamp for participant $userId in chat: $chatId');

      // Start a batch operation to update both collections
      final batch = _firestore.batch();

      // Update in participants collection
      final participantRef = _firestore
          .collection(_participantsCollection)
          .doc('${chatId}_$userId');
      batch.update(participantRef, {
        'lastReadAt': timestamp.toIso8601String(),
      });

      // Update in chat document
      final chatRef = _firestore.collection(_chatsCollection).doc(chatId);
      batch.update(chatRef, {
        'participants.$userId.lastReadAt': timestamp.toIso8601String(),
      });

      await batch.commit();

      _logger.i(
        'Successfully updated last read timestamp for participant $userId in chat $chatId',
      );
    } catch (e, stackTrace) {
      _logger.e(
        'Error updating last read timestamp: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Mute/unmute chat for a participant
  Future<void> updateMuteStatus(
    String chatId,
    String userId,
    bool isMuted,
  ) async {
    try {
      _logger.i(
        'Updating mute status for participant $userId in chat: $chatId to $isMuted',
      );

      // Start a batch operation to update both collections
      final batch = _firestore.batch();

      // Update in participants collection
      final participantRef = _firestore
          .collection(_participantsCollection)
          .doc('${chatId}_$userId');
      batch.update(participantRef, {
        'isMuted': isMuted,
      });

      // Update in chat document
      final chatRef = _firestore.collection(_chatsCollection).doc(chatId);
      batch.update(chatRef, {
        'participants.$userId.isMuted': isMuted,
      });

      await batch.commit();

      _logger.i(
        'Successfully updated mute status for participant $userId in chat $chatId',
      );
    } catch (e, stackTrace) {
      _logger.e(
        'Error updating mute status: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get participant details for a specific user in a chat
  Future<ChatParticipant?> getParticipant(
    String chatId,
    String userId,
  ) async {
    try {
      _logger.i('Fetching participant $userId for chat: $chatId');

      final doc = await _firestore
          .collection(_participantsCollection)
          .doc('${chatId}_$userId')
          .get();

      if (!doc.exists) {
        _logger.w('Participant not found: $userId in chat $chatId');
        return null;
      }

      final data = doc.data()!;
      data['id'] = doc.id;
      final participant = ChatParticipant.fromJson(data);

      _logger.i('Successfully fetched participant: $userId');
      return participant;
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching participant: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Check if user is a participant in a chat
  Future<bool> isParticipant(String chatId, String userId) async {
    try {
      _logger.i('Checking if user $userId is participant in chat: $chatId');

      final doc = await _firestore
          .collection(_participantsCollection)
          .doc('${chatId}_$userId')
          .get();

      final isParticipant = doc.exists;
      _logger.i('User $userId is participant in chat $chatId: $isParticipant');
      return isParticipant;
    } catch (e, stackTrace) {
      _logger.e(
        'Error checking participant status: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get all chats where user is a participant
  Future<List<String>> getChatsForUser(String userId) async {
    try {
      _logger.i('Fetching chats for user: $userId');

      final querySnapshot = await _firestore
          .collection(_participantsCollection)
          .where('userId', isEqualTo: userId)
          .get();

      final chatIds = <String>[];

      for (final doc in querySnapshot.docs) {
        try {
          final data = doc.data();
          final chatId = data['chatId'] as String?;
          if (chatId != null) {
            chatIds.add(chatId);
          }
        } catch (e) {
          _logger.e('Error parsing participant document ${doc.id}: $e');
        }
      }

      _logger.i('Successfully fetched ${chatIds.length} chats for user $userId');
      return chatIds;
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching chats for user: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }
}
