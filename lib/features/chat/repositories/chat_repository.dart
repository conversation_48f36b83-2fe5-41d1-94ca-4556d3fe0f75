import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logger/logger.dart';

import '../../../core/constants/firebase_collections.dart';
import '../models/chat_models.dart';
import '../enums/chat_enums.dart';

/// Repository for managing chat data in Firebase Firestore
class ChatRepository {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final Logger _logger = Logger();

  // Collection references
  static const String _chatsCollection = FirebaseCollections.chats;

  /// Get all chats for a specific user
  Future<List<ChatModel>> getChatsForUser(String userId) async {
    try {
      _logger.i('Fetching chats for user: $userId');

      final querySnapshot = await _firestore
          .collection(_chatsCollection)
          .where('participantIds', arrayContains: userId)
          .where('isActive', isEqualTo: true)
          .orderBy('lastMessageAt', descending: true)
          .get();

      final chats = <ChatModel>[];

      for (final doc in querySnapshot.docs) {
        try {
          final data = doc.data();
          data['id'] = doc.id;
          final chat = ChatModel.fromJson(data);
          chats.add(chat);
        } catch (e) {
          _logger.e('Error parsing chat document ${doc.id}: $e');
        }
      }

      _logger.i('Successfully fetched ${chats.length} chats for user $userId');
      return chats;
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching chats for user: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get a specific chat by ID
  Future<ChatModel?> getChatById(String chatId) async {
    try {
      _logger.i('Fetching chat by ID: $chatId');

      final doc = await _firestore
          .collection(_chatsCollection)
          .doc(chatId)
          .get();

      if (!doc.exists) {
        _logger.w('Chat not found: $chatId');
        return null;
      }

      final data = doc.data()!;
      data['id'] = doc.id;
      final chat = ChatModel.fromJson(data);

      _logger.i('Successfully fetched chat: ${chat.title}');
      return chat;
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching chat by ID: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get chats by type for a user
  Future<List<ChatModel>> getChatsByType(String userId, ChatType type) async {
    try {
      _logger.i('Fetching ${type.value} chats for user: $userId');

      final querySnapshot = await _firestore
          .collection(_chatsCollection)
          .where('participantIds', arrayContains: userId)
          .where('type', isEqualTo: type.value)
          .where('isActive', isEqualTo: true)
          .orderBy('lastMessageAt', descending: true)
          .get();

      final chats = <ChatModel>[];

      for (final doc in querySnapshot.docs) {
        try {
          final data = doc.data();
          data['id'] = doc.id;
          final chat = ChatModel.fromJson(data);
          chats.add(chat);
        } catch (e) {
          _logger.e('Error parsing chat document ${doc.id}: $e');
        }
      }

      _logger.i(
        'Successfully fetched ${chats.length} ${type.value} chats for user $userId',
      );
      return chats;
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching chats by type: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get chats for a specific classroom
  Future<List<ChatModel>> getChatsForClassroom(String classroomId) async {
    try {
      _logger.i('Fetching chats for classroom: $classroomId');

      final querySnapshot = await _firestore
          .collection(_chatsCollection)
          .where('classroomId', isEqualTo: classroomId)
          .where('isActive', isEqualTo: true)
          .orderBy('lastMessageAt', descending: true)
          .get();

      final chats = <ChatModel>[];

      for (final doc in querySnapshot.docs) {
        try {
          final data = doc.data();
          data['id'] = doc.id;
          final chat = ChatModel.fromJson(data);
          chats.add(chat);
        } catch (e) {
          _logger.e('Error parsing chat document ${doc.id}: $e');
        }
      }

      _logger.i(
        'Successfully fetched ${chats.length} chats for classroom $classroomId',
      );
      return chats;
    } catch (e, stackTrace) {
      _logger.e(
        'Error fetching chats for classroom: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Create a new chat
  Future<ChatModel> createChat(ChatModel chat) async {
    try {
      _logger.i('Creating new chat: ${chat.title}');

      final docRef = _firestore.collection(_chatsCollection).doc();
      final chatWithId = ChatModel(
        id: docRef.id,
        type: chat.type,
        title: chat.title,
        participantIds: chat.participantIds,
        participants: chat.participants,
        classroomId: chat.classroomId,
        settings: chat.settings,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isActive: true,
      );

      await docRef.set(chatWithId.toJson());

      _logger.i('Successfully created chat: ${chatWithId.id}');
      return chatWithId;
    } catch (e, stackTrace) {
      _logger.e('Error creating chat: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Update chat information
  Future<ChatModel> updateChat(
    String chatId,
    Map<String, dynamic> updates,
  ) async {
    try {
      _logger.i('Updating chat: $chatId');

      final updateData = {
        ...updates,
        'updatedAt': DateTime.now().toIso8601String(),
      };

      await _firestore
          .collection(_chatsCollection)
          .doc(chatId)
          .update(updateData);

      // Fetch and return updated chat
      final updatedChat = await getChatById(chatId);
      if (updatedChat == null) {
        throw Exception('Chat not found after update: $chatId');
      }

      _logger.i('Successfully updated chat: $chatId');
      return updatedChat;
    } catch (e, stackTrace) {
      _logger.e('Error updating chat: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Update last message information for a chat
  Future<void> updateLastMessage(
    String chatId,
    String messageId,
    DateTime timestamp,
  ) async {
    try {
      _logger.i('Updating last message for chat: $chatId');

      await _firestore.collection(_chatsCollection).doc(chatId).update({
        'lastMessageId': messageId,
        'lastMessageAt': timestamp.toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      });

      _logger.i('Successfully updated last message for chat: $chatId');
    } catch (e, stackTrace) {
      _logger.e(
        'Error updating last message: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Delete a chat (soft delete)
  Future<void> deleteChat(String chatId) async {
    try {
      _logger.i('Deleting chat: $chatId');

      await _firestore.collection(_chatsCollection).doc(chatId).update({
        'isActive': false,
        'updatedAt': DateTime.now().toIso8601String(),
      });

      _logger.i('Successfully deleted chat: $chatId');
    } catch (e, stackTrace) {
      _logger.e('Error deleting chat: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Get real-time stream of chats for a user
  Stream<List<ChatModel>> getChatsStreamForUser(String userId) {
    try {
      _logger.i('Setting up real-time stream for user chats: $userId');

      return _firestore
          .collection(_chatsCollection)
          .where('participantIds', arrayContains: userId)
          .where('isActive', isEqualTo: true)
          .orderBy('lastMessageAt', descending: true)
          .snapshots()
          .map((snapshot) {
            final chats = <ChatModel>[];

            for (final doc in snapshot.docs) {
              try {
                final data = doc.data();
                data['id'] = doc.id;
                final chat = ChatModel.fromJson(data);
                chats.add(chat);
              } catch (e) {
                _logger.e('Error parsing chat document ${doc.id}: $e');
              }
            }

            _logger.d(
              'Real-time update: ${chats.length} chats for user $userId',
            );
            return chats;
          });
    } catch (e) {
      _logger.e('Error setting up chats stream: $e');
      rethrow;
    }
  }

  /// Get real-time stream of a specific chat
  Stream<ChatModel?> getChatStreamById(String chatId) {
    try {
      _logger.i('Setting up real-time stream for chat: $chatId');

      return _firestore
          .collection(_chatsCollection)
          .doc(chatId)
          .snapshots()
          .map((doc) {
            if (!doc.exists) {
              _logger.w('Chat not found in stream: $chatId');
              return null;
            }

            try {
              final data = doc.data()!;
              data['id'] = doc.id;
              final chat = ChatModel.fromJson(data);
              _logger.d('Real-time update for chat: $chatId');
              return chat;
            } catch (e) {
              _logger.e('Error parsing chat document in stream: $e');
              return null;
            }
          });
    } catch (e) {
      _logger.e('Error setting up chat stream: $e');
      rethrow;
    }
  }
}
