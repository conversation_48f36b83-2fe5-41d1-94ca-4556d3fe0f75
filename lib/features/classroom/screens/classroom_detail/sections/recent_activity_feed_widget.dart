import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../../../models/activity_model.dart';
import '../../../widgets/activity_tile.dart';

/// Widget displaying recent activities in the classroom detail screen
class RecentActivityFeedWidget extends StatelessWidget {
  /// List of recent activities to display
  final List<ActivityModel> activities;

  /// Callback when an activity is tapped
  final Function(ActivityModel activity) onActivityTap;

  const RecentActivityFeedWidget({
    super.key,
    required this.activities,
    required this.onActivityTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (activities.isEmpty) {
      return _buildEmptyState(theme);
    }

    return Column(
      children: [
        ...activities.map((activity) {
          return Padding(
            padding: EdgeInsets.only(bottom: 8.h),
            child: ActivityTile(
              activity: activity,
              onTap: () => onActivityTap(activity),
              isCompact: true, // Compact version for recent feed
            ),
          );
        }),
      ],
    );
  }

  /// Build empty state when no recent activities
  Widget _buildEmptyState(ThemeData theme) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(32.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          Icon(
            Symbols.timeline,
            size: 48.sp,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          SizedBox(height: 12.h),
          Text(
            'No recent activity',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            'Activity from the last 3 days will appear here',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
