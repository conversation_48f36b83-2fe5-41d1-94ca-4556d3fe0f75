# Chat System Architecture Documentation

## Overview

This document outlines the ideal chat system architecture for Scholara, designed with a migration path from Firebase (MVP) to a scalable production system.

## Current State (MVP with Firebase)

### Tech Stack
- **Database**: Firebase Firestore
- **Real-time**: Firestore real-time listeners
- **Authentication**: Firebase Auth
- **Storage**: Firebase Storage (for media)
- **Push Notifications**: Firebase Cloud Messaging (FCM)
- **State Management**: Riverpod
- **Local Storage**: Shared Preferences / Hive

### Firebase Collections Structure
```
chats/
├── {chatId}/
│   ├── id: string
│   ├── type: "one_to_one" | "group" | "channel"
│   ├── participants: string[]
│   ├── metadata: object
│   ├── settings: object
│   └── lastMessage: object

messages/
├── {messageId}/
│   ├── id: string
│   ├── chatId: string
│   ├── senderId: string
│   ├── content: object
│   ├── timestamp: Timestamp
│   ├── status: object
│   └── reactions: object

participants/
├── {participantId}/
│   ├── chatId: string
│   ├── userId: string
│   ├── role: string
│   ├── joinedAt: Timestamp
│   └── permissions: object
```

### Current Limitations
- **Scaling**: Firestore has read/write limits
- **Cost**: Expensive at scale (per operation pricing)
- **Complex Queries**: Limited query capabilities
- **Real-time Connections**: Limited concurrent connections
- **Vendor Lock-in**: Difficult to migrate data and logic

## Target Architecture (Post-MVP)

### System Design Principles
1. **Microservices Architecture**: Modular, independently scalable services
2. **Event-Driven**: Asynchronous message processing
3. **Database Per Service**: Each service owns its data
4. **API Gateway**: Single entry point for clients
5. **Horizontal Scaling**: Scale individual components as needed

### High-Level Architecture

```mermaid
graph TB
    Client[Mobile App] --> Gateway[API Gateway]
    Gateway --> Auth[Auth Service]
    Gateway --> Chat[Chat Service]
    Gateway --> Presence[Presence Service]
    Gateway --> Notification[Notification Service]
    Gateway --> Media[Media Service]
    
    Chat --> ChatDB[(Chat Database)]
    Chat --> MessageQueue[Message Queue]
    Presence --> Redis[(Redis Cache)]
    Notification --> FCM[Firebase FCM]
    Media --> CDN[CDN Storage]
    
    MessageQueue --> Chat
    MessageQueue --> Notification
    MessageQueue --> Analytics[Analytics Service]
```

### Core Services

#### 1. Chat Service
**Responsibilities:**
- Message CRUD operations
- Chat creation and management
- Message history and pagination
- Message search and filtering

**Database:** PostgreSQL or MongoDB
**API:** GraphQL for flexible queries
**Caching:** Redis for active conversations

#### 2. Presence Service
**Responsibilities:**
- Online/offline status tracking
- Typing indicators
- Last seen timestamps
- Connection management

**Storage:** Redis (in-memory for speed)
**Protocol:** WebSocket connections
**Heartbeat:** 30-second intervals

#### 3. Notification Service
**Responsibilities:**
- Push notifications
- Email notifications
- In-app notifications
- Notification preferences

**Queue:** Apache Kafka or RabbitMQ
**Delivery:** Firebase FCM, APNs
**Storage:** PostgreSQL for notification history

#### 4. Media Service
**Responsibilities:**
- File upload and processing
- Image/video optimization
- CDN management
- Media metadata storage

**Storage:** AWS S3 or Google Cloud Storage
**CDN:** CloudFlare or AWS CloudFront
**Processing:** Background jobs for optimization

### Database Architecture

#### Primary Database Options

**Option 1: PostgreSQL (Recommended)**
```sql
-- Chats table
CREATE TABLE chats (
    id UUID PRIMARY KEY,
    type VARCHAR(20) NOT NULL,
    name VARCHAR(255),
    description TEXT,
    created_by UUID NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Messages table (partitioned by date)
CREATE TABLE messages (
    id UUID PRIMARY KEY,
    chat_id UUID NOT NULL,
    sender_id UUID NOT NULL,
    content JSONB NOT NULL,
    message_type VARCHAR(20) NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    edited_at TIMESTAMP,
    deleted_at TIMESTAMP
) PARTITION BY RANGE (created_at);

-- Participants table
CREATE TABLE chat_participants (
    id UUID PRIMARY KEY,
    chat_id UUID NOT NULL,
    user_id UUID NOT NULL,
    role VARCHAR(20) NOT NULL,
    joined_at TIMESTAMP DEFAULT NOW(),
    left_at TIMESTAMP,
    UNIQUE(chat_id, user_id)
);
```

**Option 2: MongoDB**
```javascript
// Chat collection
{
  _id: ObjectId,
  type: "one_to_one" | "group" | "channel",
  participants: [ObjectId],
  metadata: {
    name: String,
    description: String,
    avatar: String,
    createdBy: ObjectId,
    createdAt: Date
  },
  settings: {
    mutedBy: [ObjectId],
    pinnedBy: [ObjectId],
    permissions: Object
  },
  lastMessage: {
    id: ObjectId,
    content: String,
    timestamp: Date,
    senderId: ObjectId
  }
}

// Message collection (sharded by chat_id)
{
  _id: ObjectId,
  chatId: ObjectId,
  senderId: ObjectId,
  content: {
    text: String,
    type: String,
    attachments: Array
  },
  timestamp: Date,
  status: {
    sent: Date,
    delivered: Map,
    read: Map
  },
  reactions: Map,
  replyTo: ObjectId,
  editedAt: Date,
  deletedAt: Date
}
```

#### Caching Strategy (Redis)
```redis
# Active chat participants
chat:participants:{chatId} -> Set of user IDs

# User presence
user:presence:{userId} -> {status, lastSeen, currentChat}

# Typing indicators
chat:typing:{chatId} -> Set of user IDs currently typing

# Recent messages cache
chat:messages:{chatId} -> List of recent message IDs

# Unread counts
user:unread:{userId} -> Hash of chatId -> count
```

### Real-time Communication

#### WebSocket Architecture
```javascript
// Connection management
const connections = new Map(); // userId -> WebSocket connection

// Message flow
1. Client sends message via WebSocket
2. Validate and authenticate
3. Store in database
4. Publish to message queue
5. Deliver to online participants
6. Update delivery status
7. Send push notifications to offline users
```

#### Message Queue (Apache Kafka)
```
Topics:
- chat.messages.sent
- chat.messages.delivered
- chat.messages.read
- chat.presence.updated
- chat.notifications.push
```

### Migration Strategy

#### Phase 1: Preparation (Weeks 1-2)
- [ ] Set up new infrastructure (databases, services)
- [ ] Create data migration scripts
- [ ] Implement dual-write system (Firebase + new DB)
- [ ] Build API compatibility layer

#### Phase 2: Gradual Migration (Weeks 3-4)
- [ ] Migrate read operations to new system
- [ ] Validate data consistency
- [ ] Performance testing and optimization
- [ ] Gradual user migration (feature flags)

#### Phase 3: Complete Migration (Weeks 5-6)
- [ ] Switch all operations to new system
- [ ] Remove Firebase dependencies
- [ ] Data cleanup and validation
- [ ] Monitor and optimize performance

### Performance Considerations

#### Scaling Targets
- **Concurrent Users**: 100,000+
- **Messages per Second**: 10,000+
- **Message Delivery Latency**: <100ms
- **Database Response Time**: <50ms
- **WebSocket Connection Limit**: 50,000+ per server

#### Optimization Strategies
- **Database Partitioning**: Partition messages by date
- **Horizontal Scaling**: Multiple service instances
- **Connection Pooling**: Efficient database connections
- **Message Batching**: Batch notifications and updates
- **CDN**: Global content delivery for media files

### Security & Privacy

#### Authentication & Authorization
- **JWT Tokens**: Stateless authentication
- **Role-Based Access**: Chat-level permissions
- **Rate Limiting**: Prevent spam and abuse
- **Input Validation**: Sanitize all user inputs

#### Data Protection
- **Encryption at Rest**: Database encryption
- **Encryption in Transit**: TLS 1.3 for all communications
- **Data Retention**: Configurable message retention policies
- **GDPR Compliance**: User data export and deletion

#### Optional: End-to-End Encryption
- **Signal Protocol**: For maximum privacy
- **Key Management**: Secure key exchange
- **Forward Secrecy**: Protect past messages

### Monitoring & Analytics

#### Key Metrics
- **System Health**: Service uptime, response times
- **User Engagement**: Messages sent, active users
- **Performance**: Database query times, WebSocket latency
- **Business**: User retention, feature adoption

#### Logging Strategy
```json
{
  "timestamp": "2024-01-01T12:00:00Z",
  "service": "chat-service",
  "level": "INFO",
  "event": "message_sent",
  "userId": "user123",
  "chatId": "chat456",
  "messageId": "msg789",
  "latency": 45,
  "metadata": {}
}
```

### Cost Optimization

#### Firebase vs. Target Architecture
| Component | Firebase Cost | Target Cost | Savings |
|-----------|---------------|-------------|---------|
| Database Operations | $0.06/100K | $0.01/100K | 83% |
| Storage | $0.18/GB | $0.02/GB | 89% |
| Bandwidth | $0.12/GB | $0.05/GB | 58% |
| **Total Monthly** | **$5,000** | **$800** | **84%** |

*Estimated costs for 1M monthly active users*

### Development Roadmap

#### MVP Enhancement (Current)
- [ ] Optimize Firebase queries
- [ ] Implement message caching
- [ ] Add typing indicators
- [ ] Improve offline support

#### Migration Preparation (Month 1)
- [ ] Infrastructure setup
- [ ] API design and documentation
- [ ] Data migration tools
- [ ] Testing framework

#### Migration Execution (Month 2)
- [ ] Dual-write implementation
- [ ] Gradual service migration
- [ ] Performance optimization
- [ ] User migration

#### Post-Migration (Month 3+)
- [ ] Advanced features (reactions, threads)
- [ ] Analytics and insights
- [ ] AI-powered features
- [ ] Global scaling

## Conclusion

This architecture provides a clear path from the current Firebase-based MVP to a scalable, cost-effective production system. The migration strategy ensures minimal disruption while significantly improving performance, reducing costs, and enabling advanced features for future growth.
